// src/components/ToastContainer.jsx
import React from 'react';
import ToastMessage from './ToastMessage';

const ToastContainer = React.memo(({ toasts, removeToast }) => {
    return (
        <div id="toast-container" className="toast-container">
            {toasts.map(toast => (
                <ToastMessage
                    key={toast.id}
                    message={toast.message}
                    type={toast.type}
                    icon={toast.icon}
                    duration={toast.duration}
                    onClose={() => removeToast(toast.id)}
                />
            ))}
        </div>
    );
});

export default ToastContainer;