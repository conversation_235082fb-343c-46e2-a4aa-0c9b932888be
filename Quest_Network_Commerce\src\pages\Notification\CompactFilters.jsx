// src/components/CompactFilters.jsx
import React, { useRef } from 'react';
import { Cancel, Star, Archive } from '@mui/icons-material';

const CompactFilters = React.memo(({
    notifications,
    compactFilter,
    setCompactFilter,
    currentSearchQuery,
    setCurrentSearchQuery,
    onClearAllFilters
}) => {
    const searchInputRef = useRef(null);

    const getFilterCount = (filterType, filterValue) => {
        return notifications.filter(n => {
            if (filterType === 'primary') {
                if (filterValue === 'all') return !n.archived && n.status !== 'remind_later';
                if (filterValue === 'unread') return n.status === 'unread' && !n.archived;
                if (filterValue === 'starred') return n.starred;
                if (filterValue === 'archived') return n.archived;
            } else if (filterType === 'priority') {
                return n.priority === filterValue && !n.archived && n.status !== 'remind_later';
            } else if (filterType === 'category') {
                return n.type === filterValue && !n.archived && n.status !== 'remind_later';
            }
            return false;
        }).length;
    };

    const categoryTypes = [...new Set(notifications.map(n => n.type))].sort((a, b) => {
        const order = ['order', 'promotion', 'payment', 'support', 'security', 'stock', 'cart', 'review', 'system'];
        const indexA = order.indexOf(a);
        const indexB = order.indexOf(b);
        if (indexA === -1 || indexB === -1) return a.localeCompare(b);
        return indexA - indexB;
    });

    const priorityLevels = ['High', 'Medium', 'Low'];

    return (
        <div className="mb-4 flex flex-wrap gap-2 items-center" id="compact-filter-buttons-container">
            <div className="relative flex min-w-[200px] max-w-xs">
                <input
                    type="text"
                    id="notification-search-compact"
                    placeholder="Search notifications..."
                    className="shadow-sm"
                    value={currentSearchQuery}
                    onChange={(e) => setCurrentSearchQuery(e.target.value)}
                    ref={searchInputRef}
                />
                <button
                    id="clear-search-compact"
                    className={`bg-none border-none ${currentSearchQuery === '' ? 'hidden' : ''}`}
                    onClick={() => setCurrentSearchQuery('')}
                >
                    <Cancel className="text-light-blue text-xl mr-2 w-5 text-center" fontSize="small" />
                </button>
            </div>
            <button
                className={`filter-btn ${compactFilter === 'all' ? 'active-filter' : ''}`}
                data-filter-type="primary"
                data-filter="all"
                onClick={() => setCompactFilter('all')}
            >
                All <span className="count">{getFilterCount('primary', 'all')}</span>
            </button>
            <button
                className={`filter-btn ${compactFilter === 'unread' ? 'active-filter unread-filter' : ''}`}
                data-filter-type="primary"
                data-filter="unread"
                onClick={() => setCompactFilter('unread')}
            >
                Unread <span className="count">{getFilterCount('primary', 'unread')}</span>
            </button>
            <button
                className={`filter-btn ${compactFilter === 'starred' ? 'active-filter' : ''}`}
                data-filter-type="primary"
                data-filter="starred"
                onClick={() => setCompactFilter('starred')}
            >
                <Star style={{ color: 'var(--color-gold)' }} fontSize="small" /> Starred <span className="count">{getFilterCount('primary', 'starred')}</span>
            </button>
            <button
                className={`filter-btn ${compactFilter === 'archived' ? 'active-filter' : ''}`}
                data-filter-type="primary"
                data-filter="archived"
                onClick={() => setCompactFilter('archived')}
            >
                <Archive fontSize="small" /> Archived <span className="count">{getFilterCount('primary', 'archived')}</span>
            </button>

            {priorityLevels.map(priority => (
                <button
                    key={priority}
                    className={`filter-btn ${compactFilter === priority ? 'active-filter' : ''}`}
                    data-filter-type="priority"
                    data-filter={priority}
                    onClick={() => setCompactFilter(priority)}
                >
                    {priority} Priority <span className="count">{getFilterCount('priority', priority)}</span>
                </button>
            ))}

            {categoryTypes.map(type => (
                <button
                    key={type}
                    className={`filter-btn ${compactFilter === type ? 'active-filter' : ''}`}
                    data-filter-type="category"
                    data-filter={type}
                    onClick={() => setCompactFilter(type)}
                >
                    {type.charAt(0).toUpperCase() + type.slice(1)} <span className="count">{getFilterCount('category', type)}</span>
                </button>
            ))}

            <button className="filter-btn" onClick={onClearAllFilters}>Clear Filters</button>
        </div>
    );
});

export default CompactFilters;