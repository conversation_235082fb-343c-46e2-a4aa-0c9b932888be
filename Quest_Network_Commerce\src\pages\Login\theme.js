import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  typography: {
    fontFamily: "'HCLTechRoobert', '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif",
    h1: {
      fontFamily: "'HCLT<PERSON><PERSON>oo<PERSON>', '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, 'Helvetica Neue', <PERSON>l, sans-serif",
    },
    h2: {
      fontFamily: "'HCLTechRoobert', '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif",
    },
    h3: {
      fontFamily: "'HCLTechRoobert', '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif",
    },
    h4: {
      fontFamily: "'HCLTechRoobert', '<PERSON>oo<PERSON>', <PERSON>o, 'Helvetica Neue', Arial, sans-serif",
    },
    h5: {
      fontFamily: "'HCLTechRoobert', '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif",
    },
    h6: {
      fontFamily: "'HCLTech<PERSON>oo<PERSON>', '<PERSON><PERSON><PERSON>', <PERSON>o, 'Helvetica Neue', Aria<PERSON>, sans-serif",
    },
    body1: {
      fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
    },
    body2: {
      fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
    },
    button: {
      fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
    },
    caption: {
      fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
    },
    overline: {
      fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
    },
  },
  components: {
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiInputBase-input': {
            fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
          },
          '& .MuiInputLabel-root': {
            fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
          },
          '& .MuiFormHelperText-root': {
            fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
        },
      },
    },
    MuiTypography: {
      styleOverrides: {
        root: {
          fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
        },
      },
    },
    MuiFormControlLabel: {
      styleOverrides: {
        label: {
          fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
        },
      },
    },
    MuiSelect: {
      styleOverrides: {
        select: {
          fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
        },
      },
    },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        label: {
          fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
        },
      },
    },
  },
});

export default theme;
