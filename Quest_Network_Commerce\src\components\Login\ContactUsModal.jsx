import { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  IconButton,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Chip,
  Autocomplete,
} from "@mui/material";
import { Close, AttachFile, Clear } from "@mui/icons-material";

const ContactUsModal = ({ open, onClose }) => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    countryCode: "+1",
    subject: [],
    description: "",
    attachments: []
  });

  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const subjectOptions = [
    "Login Issues",
    "Password Reset Problems",
    "Account Access Denied",
    "Two-Factor Authentication Issues",
    "Email Verification Problems",
    "Account Locked/Suspended",
    "Username/Email Not Recognized",
    "Social Media Login Issues",
    "Browser Compatibility Issues",
    "Mobile App Login Problems",
    "Other"
  ];

  const countryCodes = [
    { code: "+1", country: "United States", name: "US" },
    { code: "+1", country: "Canada", name: "CA" },
    { code: "+44", country: "United Kingdom", name: "GB" },
    { code: "+91", country: "India", name: "IN" },
    { code: "+86", country: "China", name: "CN" },
    { code: "+49", country: "Germany", name: "DE" },
    { code: "+33", country: "France", name: "FR" },
    { code: "+81", country: "Japan", name: "JP" },
    { code: "+61", country: "Australia", name: "AU" },
    { code: "+55", country: "Brazil", name: "BR" },
    { code: "+7", country: "Russia", name: "RU" },
    { code: "+39", country: "Italy", name: "IT" },
    { code: "+34", country: "Spain", name: "ES" },
    { code: "+82", country: "South Korea", name: "KR" },
    { code: "+52", country: "Mexico", name: "MX" },
    { code: "+31", country: "Netherlands", name: "NL" },
    { code: "+46", country: "Sweden", name: "SE" },
    { code: "+47", country: "Norway", name: "NO" },
    { code: "+41", country: "Switzerland", name: "CH" },
    { code: "+65", country: "Singapore", name: "SG" },
    { code: "+971", country: "UAE", name: "AE" },
    { code: "+966", country: "Saudi Arabia", name: "SA" },
    { code: "+27", country: "South Africa", name: "ZA" },
    { code: "+20", country: "Egypt", name: "EG" },
    { code: "+234", country: "Nigeria", name: "NG" }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubjectChange = (event, newValue) => {
    setFormData(prev => ({
      ...prev,
      subject: newValue
    }));
  };

  const handleFileAttachment = (event) => {
    const files = Array.from(event.target.files);
    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...files]
    }));
  };

  const removeAttachment = (index) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
  };

  const isOtherSelected = formData.subject.includes("Other");

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone) => {
    const phoneRegex = /^[\d\s\-\+\(\)]+$/;
    return phone.length >= 10 && phoneRegex.test(phone);
  };

  const handleSubmit = () => {
    // Validate required fields
    if (!formData.email || !formData.phone || formData.subject.length === 0) {
      alert("Please fill in all required fields (Email, Phone, Subject)");
      return;
    }

    if (!validateEmail(formData.email)) {
      alert("Please enter a valid email address");
      return;
    }

    if (!validatePhone(formData.phone)) {
      alert("Please enter a valid phone number");
      return;
    }

    if (isOtherSelected && !formData.description.trim()) {
      alert("Description is required when 'Other' is selected");
      return;
    }

    console.log("Contact Us Data:", formData);
    setShowSuccessMessage(true);
    
    // Hide success message and close modal after 2 seconds
    setTimeout(() => {
      setShowSuccessMessage(false);
      onClose();
      // Reset form
      setFormData({
        name: "",
        email: "",
        phone: "",
        countryCode: "+1",
        subject: [],
        description: "",
        attachments: []
      });
    }, 2000);
  };

  if (showSuccessMessage) {
    return (
      <Dialog 
        open={open} 
        onClose={() => {}} 
        maxWidth="sm" 
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: "16px",
            textAlign: "center",
            p: 4
          }
        }}
      >
        <DialogContent>
          <Typography variant="h5" sx={{ 
            color: "#2EC0CB", 
            fontWeight: 600, 
            mb: 2,
            fontFamily: "'HCLTechRoobert', sans-serif"
          }}>
            ✅ Ticket Raised Successfully!
          </Typography>
          <Typography variant="body1" sx={{ color: "#666" }}>
            We've received your request and will get back to you soon.
          </Typography>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: "16px",
          maxHeight: "90vh"
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        background: "linear-gradient(to right, #2EC0CB, #23A3AD)",
        color: "white",
        fontFamily: "'HCLTechRoobert', sans-serif"
      }}>
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          Contact Us - Support Request
        </Typography>
        <IconButton onClick={onClose} sx={{ color: "white" }}>
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 1 }}>
          {/* Name Field */}
          <TextField
            label="Full Name (Optional)"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            fullWidth
            sx={{
              '& .MuiOutlinedInput-root': {
                '&.Mui-focused fieldset': {
                  borderColor: '#2EC0CB',
                },
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: '#2EC0CB',
              },
            }}
          />

          {/* Email Field */}
          <TextField
            label="Email Address *"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            fullWidth
            required
            sx={{
              '& .MuiOutlinedInput-root': {
                '&.Mui-focused fieldset': {
                  borderColor: '#2EC0CB',
                },
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: '#2EC0CB',
              },
            }}
          />

          {/* Phone Field */}
          <Box sx={{ display: 'flex', gap: 1 }}>
            <FormControl sx={{ minWidth: 120 }}>
              <InputLabel>Country</InputLabel>
              <Select
                value={formData.countryCode}
                onChange={(e) => handleInputChange('countryCode', e.target.value)}
                label="Country"
                sx={{
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#2EC0CB',
                  },
                }}
              >
                {countryCodes.map((country) => (
                  <MenuItem key={`${country.code}-${country.name}`} value={country.code}>
                    {country.code} {country.country}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              label="Phone Number *"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              fullWidth
              required
              sx={{
                '& .MuiOutlinedInput-root': {
                  '&.Mui-focused fieldset': {
                    borderColor: '#2EC0CB',
                  },
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: '#2EC0CB',
                },
              }}
            />
          </Box>

          {/* Subject Selection */}
          <Autocomplete
            multiple
            options={subjectOptions}
            value={formData.subject}
            onChange={handleSubjectChange}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  variant="outlined"
                  label={option}
                  {...getTagProps({ index })}
                  sx={{
                    borderColor: '#2EC0CB',
                    color: '#2EC0CB',
                    '& .MuiChip-deleteIcon': {
                      color: '#2EC0CB',
                    },
                  }}
                />
              ))
            }
            renderInput={(params) => (
              <TextField
                {...params}
                label="Subject *"
                placeholder="Select login issues..."
                required
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '&.Mui-focused fieldset': {
                      borderColor: '#2EC0CB',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#2EC0CB',
                  },
                }}
              />
            )}
            sx={{
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: '#2EC0CB',
              },
            }}
          />

          {/* Description */}
          <TextField
            label={isOtherSelected ? "Description *" : "Description (Optional)"}
            multiline
            rows={4}
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            fullWidth
            required={isOtherSelected}
            placeholder="Please describe your issue in detail..."
            sx={{
              '& .MuiOutlinedInput-root': {
                '&.Mui-focused fieldset': {
                  borderColor: '#2EC0CB',
                },
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: '#2EC0CB',
              },
            }}
          />

          {/* File Attachments */}
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1, color: '#666' }}>
              Attachments (Optional)
            </Typography>
            <Button
              component="label"
              variant="outlined"
              startIcon={<AttachFile />}
              sx={{
                borderColor: '#2EC0CB',
                color: '#2EC0CB',
                '&:hover': {
                  borderColor: '#23A3AD',
                  backgroundColor: '#E0F7F9',
                },
              }}
            >
              Attach Files
              <input
                type="file"
                hidden
                multiple
                onChange={handleFileAttachment}
                accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.txt"
              />
            </Button>

            {formData.attachments.length > 0 && (
              <Box sx={{ mt: 2 }}>
                {formData.attachments.map((file, index) => (
                  <Chip
                    key={index}
                    label={file.name}
                    onDelete={() => removeAttachment(index)}
                    deleteIcon={<Clear />}
                    sx={{
                      m: 0.5,
                      borderColor: '#2EC0CB',
                      color: '#2EC0CB',
                    }}
                    variant="outlined"
                  />
                ))}
              </Box>
            )}
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button
          onClick={onClose}
          sx={{
            color: '#666',
            '&:hover': {
              backgroundColor: '#f5f5f5'
            }
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          sx={{
            background: "linear-gradient(to right, #2EC0CB, #23A3AD)",
            fontWeight: 600,
            px: 3,
            '&:hover': {
              background: "linear-gradient(to right, #23A3AD, #2EC0CB)",
            }
          }}
        >
          Raise Ticket
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ContactUsModal;
