// src/components/ToastMessage.jsx
import React, { useEffect, useState } from 'react';
import { Close } from '@mui/icons-material';

const ToastMessage = React.memo(({ message, type, icon: IconComponent, duration, onClose }) => {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        const timer = setTimeout(() => {
            setIsVisible(true);
        }, 10);

        const autoCloseTimer = setTimeout(() => {
            setIsVisible(false);
            const fadeOutTimer = setTimeout(() => {
                onClose();
            }, 500);
            return () => clearTimeout(fadeOutTimer);
        }, duration);

        return () => {
            clearTimeout(timer);
            clearTimeout(autoCloseTimer);
        };
    }, [duration, onClose]);

    const handleClose = () => {
        setIsVisible(false);
        setTimeout(() => {
            onClose();
        }, 500);
    };

    return (
        <div className={`toast-message ${type} ${isVisible ? 'slide-in' : 'slide-out'}`}>
            {IconComponent && <IconComponent fontSize="small" className="mr-2" />}
            <span className="flex-1">{message}</span>
            <button className="close-toast" onClick={handleClose}>
                <Close fontSize="small" />
            </button>
        </div>
    );
});

export default ToastMessage;