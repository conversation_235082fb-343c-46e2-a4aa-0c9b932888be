// src/components/NotificationCard.jsx
import React from 'react';
import { getNotificationIconAndColor, getPriorityBadgeClass, formatRelativeTime } from '../Notification/helpers';
import { CheckCircle, Autorenew, VisibilityOff, Star, StarBorder, Archive, Unarchive, Delete, Schedule } from '@mui/icons-material';

const NotificationCard = React.memo(({ notification, onToggleSelect, onAction, onCardClick, isSelected, buttonFeedback = {} }) => {
    const { id, type, status, archived, starred, priority, title, message, timestamp } = notification;
    const { icon, color, border } = getNotificationIconAndColor(type);
    const TypeIcon = icon;
    const priorityClass = getPriorityBadgeClass(priority);

    // Helper for feedback state
    const getBtnFeedback = (action) => buttonFeedback[`${id}-${action}`] || {};

    // Mark as Read/Unread toggle
    const isRead = status === 'read' || status === 'remind_later';
    const markBtnFeedback = getBtnFeedback(isRead ? 'mark-unread' : 'mark-read');
    const markButtonHtml = isRead ?
        <button className={`mark-unread-btn flex items-center gap-1 ${markBtnFeedback.isLoading ? 'opacity-60 pointer-events-none' : ''}`}
            data-action="mark-unread" data-id={id}
            onClick={e => { e.stopPropagation(); onAction(id, 'mark-unread', { buttonId: `${id}-mark-unread` }); }}
            aria-label="Mark as Unread" disabled={markBtnFeedback.isLoading} title="Mark as Unread">
            {markBtnFeedback.isLoading ? <Autorenew className="animate-spin" /> : <VisibilityOff fontSize="small" />}
            {markBtnFeedback.isSuccess ? <CheckCircle className="text-green-600" fontSize="small" /> : null}
            Unread
        </button>
        :
        <button className={`mark-read-btn flex items-center gap-1 ${markBtnFeedback.isLoading ? 'opacity-60 pointer-events-none' : ''}`}
            data-action="mark-read" data-id={id}
            onClick={e => { e.stopPropagation(); onAction(id, 'mark-read', { buttonId: `${id}-mark-read` }); }}
            aria-label="Mark as Read" disabled={markBtnFeedback.isLoading} title="Mark as Read">
            {markBtnFeedback.isLoading ? <Autorenew className="animate-spin" /> : <CheckCircle fontSize="small" />}
            {markBtnFeedback.isSuccess ? <CheckCircle className="text-green-600" fontSize="small" /> : null}
            Read
        </button>;

    // Star/Unstar toggle
    const starBtnFeedback = getBtnFeedback('toggle-star');
    const starButtonClass = starred ? 'active' : '';
    const starButtonIcon = starred ? <Star fontSize="small" /> : <StarBorder fontSize="small" />;
    const starButtonHtml = (
        <button className={`star-btn flex items-center gap-1 ${starButtonClass} ${starBtnFeedback.isLoading ? 'opacity-60 pointer-events-none' : ''}`}
            data-action="toggle-star" data-id={id}
            onClick={e => { e.stopPropagation(); onAction(id, 'toggle-star', { buttonId: `${id}-toggle-star` }); }}
            aria-label={starred ? 'Unstar' : 'Star'} disabled={starBtnFeedback.isLoading} title={starred ? 'Unstar' : 'Star'}>
            {starBtnFeedback.isLoading ? <Autorenew className="animate-spin" /> : starButtonIcon}
            {starBtnFeedback.isSuccess ? <CheckCircle className="text-yellow-500" fontSize="small" /> : null}
            {starred ? 'Unstar' : 'Star'}
        </button>
    );

    // Archive/Unarchive toggle
    const archiveBtnFeedback = getBtnFeedback(archived ? 'unarchive' : 'archive');
    const archiveButtonHtml = archived ? (
        <button className={`archive-btn flex items-center gap-1 ${archiveBtnFeedback.isLoading ? 'opacity-60 pointer-events-none' : ''}`}
            data-action="unarchive" data-id={id}
            onClick={e => { e.stopPropagation(); onAction(id, 'unarchive', { buttonId: `${id}-unarchive` }); }}
            aria-label="Unarchive" disabled={archiveBtnFeedback.isLoading} title="Unarchive">
            {archiveBtnFeedback.isLoading ? <Autorenew className="animate-spin" /> : <Unarchive fontSize="small" />}
            {archiveBtnFeedback.isSuccess ? <CheckCircle className="text-green-600" fontSize="small" /> : null}
            Unarchive
        </button>
    ) : (
        <button className={`archive-btn flex items-center gap-1 ${archiveBtnFeedback.isLoading ? 'opacity-60 pointer-events-none' : ''}`}
            data-action="archive" data-id={id}
            onClick={e => { e.stopPropagation(); onAction(id, 'archive', { buttonId: `${id}-archive` }); }}
            aria-label="Archive" disabled={archiveBtnFeedback.isLoading} title="Archive">
            {archiveBtnFeedback.isLoading ? <Autorenew className="animate-spin" /> : <Archive fontSize="small" />}
            {archiveBtnFeedback.isSuccess ? <CheckCircle className="text-green-600" fontSize="small" /> : null}
            Archive
        </button>
    );

    // Delete button
    const deleteBtnFeedback = getBtnFeedback('delete');
    const deleteButtonHtml = (
        <button className={`delete-btn flex items-center gap-1 ${deleteBtnFeedback.isLoading ? 'opacity-60 pointer-events-none' : ''}`}
            data-action="delete" data-id={id}
            onClick={e => { e.stopPropagation(); onAction(id, 'delete', { buttonId: `${id}-delete` }); }}
            aria-label="Delete" disabled={deleteBtnFeedback.isLoading} title="Delete">
            {deleteBtnFeedback.isLoading ? <Autorenew className="animate-spin" /> : <Delete fontSize="small" />}
            {deleteBtnFeedback.isSuccess ? <CheckCircle className="text-green-600" fontSize="small" /> : null}
            Delete
        </button>
    );

    const handleCheckboxChange = (e) => {
        onToggleSelect(id);
    };

    const handleCheckboxClick = (e) => {
        e.stopPropagation(); // Crucial: Prevents the click from bubbling up to the parent card div
    };

    return (
        <div
            className={`notification-card ${status === 'unread' ? 'unread' : ''} ${archived ? 'archived' : ''} ${status === 'remind_later' ? 'remind-later' : ''} ${starred ? 'starred' : ''} ${border}`}
            data-id={id}
            onClick={() => onCardClick(id)}
        >
            <input
                type="checkbox"
                className="mr-2 mt-1 h-4 w-4 cursor-pointer notification-checkbox"
                data-id={id}
                checked={isSelected}
                onChange={handleCheckboxChange}
                onClick={handleCheckboxClick}
            />
            <div className="notification-content">
                <div className="notification-main-info">
                    {TypeIcon && React.createElement(TypeIcon, { fontSize: 'small', className: color })}
                    <span className="notification-title">{title}</span>
                    {priority && <span className={`priority-badge ${priorityClass}`}>{priority}</span>}
                    <span className="notification-timestamp">{formatRelativeTime(timestamp)}</span>
                </div>
                <div className="notification-message pl-7">{message}</div>
            </div>
            <div className="notification-card-actions">
                {starButtonHtml}
                {markButtonHtml}
                <button className="remind-later-btn flex items-center gap-1" data-action="remind-later" data-id={id} onClick={e => { e.stopPropagation(); onAction(id, 'remind-later', { buttonId: `${id}-remind-later` }); }}>
                    <Schedule fontSize="small" /> Remind
                </button>
                {archiveButtonHtml}
                {deleteButtonHtml}
            </div>
        </div>
    );
});

export default NotificationCard;