import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { Link, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import Button from "@mui/material/Button";
import Checkbox from "@mui/material/Checkbox";
import FormControlLabel from "@mui/material/FormControlLabel";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import AuthLayout from "../../components/Login/AuthLayout";
import SmartPhoneInput from "../../components/Login/SmartPhoneInput";
import StyledTextField from "../../components/Login/StyledTextField";
import CustomSnackbar from "../../components/Login/CustomSnackbar";
import SocialAuthDialog from "../../components/Login/SocialAuthDialog";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Chip,
  IconButton,
  Autocomplete,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
} from "@mui/material";
import { Close, AttachFile, Clear, CloudUpload, Delete } from "@mui/icons-material";


import {
  Fingerprint,
  Face,
  Google,
  Facebook,
  Apple,
  Microsoft
} from "@mui/icons-material";

const Login = () => {
  const [socialDialog, setSocialDialog] = useState({
    open: false,
    provider: '',
    icon: null
  });
  const [contactUsOpen, setContactUsOpen] = useState(false);
  const [contactForm, setContactForm] = useState({
    name: "",
    email: "",
    phone: "",
    countryCode: "+1",
    subject: [],
    description: "",
    attachments: []
  });
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);



  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: {
      identifier: "",
      password: ""
    }
  });

  // Register the identifier field
  useEffect(() => {
    register("identifier", { required: "This field is required" });
  }, [register]);

  const navigate = useNavigate();

  const onSubmit = (data) => {
    console.log("Login data:", data);
    navigate("/login/mfa-verify");
  };

  const handleGuest = () => {
    console.log("Guest user activated");
    navigate("/home");
  };



  const handleIdentifierChange = (e) => {
    setValue("identifier", e.target.value);
  };

  const handleSocialAuth = (provider, icon) => {
    setSocialDialog({
      open: true,
      provider,
      icon
    });
  };

  const handleCloseSocialDialog = () => {
    setSocialDialog({
      open: false,
      provider: '',
      icon: null
    });
  };

  // Helper function to get Material UI icons for social media
  const getSocialIcon = (iconName) => {
    const iconProps = { sx: { fontSize: 20, color: '#666' } };
    switch (iconName) {
      case 'google':
        return <Google {...iconProps} sx={{ ...iconProps.sx, color: '#DB4437' }} />;
      case 'facebook':
        return <Facebook {...iconProps} sx={{ ...iconProps.sx, color: '#4267B2' }} />;
      case 'apple':
        return <Apple {...iconProps} sx={{ ...iconProps.sx, color: '#000' }} />;

      case 'microsoft':
        return <Microsoft {...iconProps} sx={{ ...iconProps.sx, color: '#00A1F1' }} />;
      default:
        return null;
    }
  };

  return (
    <AuthLayout title="Welcome Back" subtitle="Sign in with your email">
      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>


        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          <form onSubmit={handleSubmit(onSubmit)} style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
            <SmartPhoneInput
              label="Username / Phone Number / Email"
              placeholder="Enter username, phone number, or email"
              value={watch("identifier")}
              onChange={handleIdentifierChange}
              error={!!errors.identifier}
              helperText={errors.identifier?.message}
              name="identifier"
              showCountrySelector={true}
            />

            <StyledTextField
              label="Password"
              type="password"
              showPasswordToggle={true}
              {...register("password", {
                required: "Password is required",
                minLength: {
                  value: 8,
                  message: "Password must be at least 8 characters long"
                },
                pattern: {
                  value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
                  message: "Password must contain at least 1 uppercase, 1 lowercase, 1 number, and 1 special character"
                }
              })}
              error={!!errors.password}
              helperText={errors.password?.message}
              validation={{
                required: true,
                password: true
              }}
            />

            <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
              <FormControlLabel
                control={<Checkbox sx={{ color: "#2EC0CB" }} />}
                label="Remember me"
                sx={{ color: "#666" }}
              />
              <Link
                to="forgot-password"
                style={{
                  color: "#2EC0CB",
                  fontSize: "0.875rem",
                  textDecoration: "none",
                  fontWeight: 600
                }}
              >
                Forgot Password?
              </Link>
            </Box>

            <Button
              variant="contained"
              fullWidth
              type="submit"
              sx={{
                background: "linear-gradient(to right, #2EC0CB, #23A3AD)",
                fontWeight: "bold",
                py: 1.2,
                fontSize: "0.9rem",
                borderRadius: "12px",
                textTransform: "none",
                height: "48px",
                ":hover": {
                  background: "linear-gradient(to right, #23A3AD, #2EC0CB)",
                },
              }}
            >
              Login
            </Button>
          </form>

          {/* OR Divider */}
          <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>
            <Box sx={{ flex: 1, height: '1px', backgroundColor: '#E0E0E0' }} />
            <Typography
              sx={{
                mx: 3,
                color: '#666',
                fontSize: '0.9rem',
                fontWeight: 'bold',
                backgroundColor: 'white',
                px: 2
              }}
            >
              OR
            </Typography>
            <Box sx={{ flex: 1, height: '1px', backgroundColor: '#E0E0E0' }} />
          </Box>

          {/* Social Media and Biometric Icons Row - Below Login Button */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', gap: 6 }}>
            {/* Social Media Icons - Left Side */}
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Typography
                sx={{
                  fontSize: '0.8rem',
                  color: '#666',
                  mb: 2,
                  fontWeight: '500',
                  textAlign: 'center'
                }}
              >
                Login using
              </Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                {["google", "facebook", "apple", "microsoft"].map((provider) => (
                  <Button
                    key={provider}
                    onClick={() => handleSocialAuth(
                      provider.charAt(0).toUpperCase() + provider.slice(1),
                      getSocialIcon(provider)
                    )}
                    sx={{
                      padding: 1,
                      minWidth: 'auto',
                      borderRadius: "50%",
                      border: "2px solid #2EC0CB",
                      backgroundColor: 'white',
                      width: 40,
                      height: 40,
                      "&:hover": {
                        backgroundColor: "#E0F7F9",
                        borderColor: "#23A3AD"
                      }
                    }}
                  >
                    {getSocialIcon(provider)}
                  </Button>
                ))}
              </Box>
            </Box>

            {/* Biometric Icons - Right Side */}
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Typography
                sx={{
                  fontSize: '0.8rem',
                  color: '#666',
                  mb: 2,
                  fontWeight: '500',
                  textAlign: 'center'
                }}
              >
                Biometrics
              </Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  onClick={() => alert("Face ID login not connected yet")}
                  sx={{
                    padding: 1,
                    borderRadius: "50%",
                    border: "2px solid #2EC0CB",
                    minWidth: 'auto',
                    width: 40,
                    height: 40,
                    "&:hover": {
                      backgroundColor: "#E0F7F9",
                      borderColor: "#23A3AD"
                    }
                  }}
                >
                  <Face sx={{ color: "#2EC0CB", fontSize: 20 }} />
                </Button>
                <Button
                  onClick={() => alert("Fingerprint login not connected yet")}
                  sx={{
                    padding: 1,
                    borderRadius: "50%",
                    border: "2px solid #2EC0CB",
                    minWidth: 'auto',
                    width: 40,
                    height: 40,
                    "&:hover": {
                      backgroundColor: "#E0F7F9",
                      borderColor: "#23A3AD"
                    }
                  }}
                >
                  <Fingerprint sx={{ color: "#2EC0CB", fontSize: 20 }} />
                </Button>
              </Box>
            </Box>
          </Box>



          <Typography align="center" sx={{ mt: 3, fontSize: "0.9rem" }}>
            Don’t have an account?{" "}
            <Link
              to="register"
              style={{
                color: "#2EC0CB",
                fontWeight: 600,
                textDecoration: "none"
              }}
            >
              Sign Up
            </Link>{" "}
            <span style={{ color: "gray" }}>or</span>{" "}
            <button
              onClick={handleGuest}
              style={{
                color: "#2EC0CB",
                fontWeight: "600",
                background: "none",
                border: "none",
                cursor: "pointer",
                padding: 0,

              }}
            >
              Continue as Guest
            </button>
          </Typography>

          <Typography align="center" sx={{ mt: 1, fontSize: "0.8rem" }}>
            Having issues?{" "}
            <span
              onClick={() => setContactUsOpen(true)}
              style={{
                color: "#2EC0CB",
                fontWeight: 600,
                textDecoration: "none",
                cursor: "pointer"
              }}
            >
              Contact Us
            </span>
          </Typography>


        </motion.div>

        <SocialAuthDialog
          open={socialDialog.open}
          onClose={handleCloseSocialDialog}
          provider={socialDialog.provider}
          icon={socialDialog.icon}
        />

        {/* Contact Us Modal */}
        <Dialog
          open={contactUsOpen}
          onClose={() => setContactUsOpen(false)}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: "20px",
              maxHeight: "95vh",
              background: "linear-gradient(135deg, #ffffff 0%, #f8fdfe 100%)",
              boxShadow: "0 20px 60px rgba(46, 192, 203, 0.15)",
              overflow: "hidden"
            }
          }}
        >
          <DialogTitle sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            background: "linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)",
            color: "white",
            fontFamily: "'HCLTechRoobert', sans-serif",
            padding: "24px 32px",
            borderBottom: "none"
          }}>
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 700, mb: 0.5 }}>
                Contact Support
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9, fontWeight: 400 }}>
                We're here to help you with any login or account issues
              </Typography>
            </Box>
            <IconButton
              onClick={() => setContactUsOpen(false)}
              sx={{
                color: "white",
                backgroundColor: "rgba(255,255,255,0.1)",
                "&:hover": {
                  backgroundColor: "rgba(255,255,255,0.2)"
                }
              }}
            >
              <Close />
            </IconButton>
          </DialogTitle>

          <DialogContent sx={{ p: "32px", pt: "24px", backgroundColor: "transparent" }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 2 }}>
              <StyledTextField
                label="Full Name (Optional)"
                value={contactForm.name}
                onChange={(e) => setContactForm(prev => ({ ...prev, name: e.target.value }))}
                fullWidth
                placeholder="Enter your full name"
                validation={{
                  maxLength: 50
                }}
              />

              <StyledTextField
                label="Email Address *"
                type="email"
                value={contactForm.email}
                onChange={(e) => setContactForm(prev => ({ ...prev, email: e.target.value }))}
                fullWidth
                required
                placeholder="Enter your email address"
                validation={{
                  required: true,
                  email: true
                }}
              />

              <StyledTextField
                label="Phone Number *"
                value={contactForm.phone}
                onChange={(e) => setContactForm(prev => ({ ...prev, phone: e.target.value }))}
                fullWidth
                required
                placeholder="Enter your phone number"
                validation={{
                  required: true,
                  phone: true
                }}
              />

              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 600, color: '#333' }}>
                    Subject *
                  </Typography>
                  {contactForm.subject.length > 0 && (
                    <Button
                      size="small"
                      onClick={() => setContactForm(prev => ({ ...prev, subject: [] }))}
                      sx={{
                        color: '#2EC0CB',
                        fontSize: '0.75rem',
                        textTransform: 'none',
                        minWidth: 'auto',
                        padding: '2px 8px',
                        '&:hover': {
                          backgroundColor: '#E0F7F9'
                        }
                      }}
                    >
                      Clear All
                    </Button>
                  )}
                </Box>
                <FormControl fullWidth required>
                  <Select
                    multiple
                    value={contactForm.subject}
                    onChange={(e) => setContactForm(prev => ({ ...prev, subject: e.target.value }))}
                    displayEmpty
                    onClose={(e) => e.stopPropagation()}
                    renderValue={(selected) => {
                      if (selected.length === 0) {
                        return <Typography sx={{ color: '#999' }}>Select issue categories</Typography>;
                      }
                      return (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip
                              key={value}
                              label={value}
                              size="small"
                              onDelete={(e) => {
                                e.stopPropagation();
                                setContactForm(prev => ({
                                  ...prev,
                                  subject: prev.subject.filter(s => s !== value)
                                }));
                              }}
                              onMouseDown={(e) => e.stopPropagation()}
                              sx={{
                                backgroundColor: '#E0F7F9',
                                color: '#2EC0CB',
                                fontWeight: 500,
                                '& .MuiChip-deleteIcon': {
                                  color: '#2EC0CB',
                                  '&:hover': {
                                    color: '#23A3AD'
                                  }
                                }
                              }}
                            />
                          ))}
                        </Box>
                      );
                    }}
                    sx={{
                      borderRadius: '12px',
                      backgroundColor: '#ffffff',
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#E0E7FF',
                        borderWidth: '2px',
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#2EC0CB',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#2EC0CB',
                        borderWidth: '2px',
                      },
                      '& .MuiSelect-select': {
                        padding: '16px 14px',
                        minHeight: '24px',
                      }
                    }}
                  >
                    {[
                      "Login Issues",
                      "Password Reset Problems",
                      "Account Access Denied",
                      "Two-Factor Authentication Issues",
                      "Email Verification Problems",
                      "Account Locked/Suspended",
                      "Username/Email Not Recognized",
                      "Social Media Login Issues",
                      "Browser Compatibility Issues",
                      "Mobile App Login Problems",
                      "Other"
                    ].map((option) => (
                      <MenuItem
                        key={option}
                        value={option}
                        sx={{
                          '&:hover': {
                            backgroundColor: '#E0F7F9'
                          },
                          '&.Mui-selected': {
                            backgroundColor: '#E0F7F9',
                            '&:hover': {
                              backgroundColor: '#B8F0F3'
                            }
                          }
                        }}
                      >
                        {option}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>

              <TextField
                label={contactForm.subject.includes("Other") ? "Description *" : "Description (Optional)"}
                multiline
                rows={4}
                value={contactForm.description}
                onChange={(e) => setContactForm(prev => ({ ...prev, description: e.target.value }))}
                fullWidth
                required={contactForm.subject.includes("Other")}
                placeholder="Please describe your issue in detail..."
                variant="outlined"
                sx={{
                  borderRadius: '12px',
                  backgroundColor: '#ffffff',
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '12px',
                    '& fieldset': {
                      borderColor: '#E0E7FF',
                      borderWidth: '2px',
                    },
                    '&:hover fieldset': {
                      borderColor: '#2EC0CB',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#2EC0CB',
                      borderWidth: '2px',
                    },
                    '& textarea': {
                      color: '#333',
                      fontSize: '16px',
                      lineHeight: '1.5',
                      '&::placeholder': {
                        color: '#999',
                        opacity: 1,
                      }
                    }
                  },
                  '& .MuiInputLabel-root': {
                    color: '#666',
                    fontSize: '16px',
                    '&.Mui-focused': {
                      color: '#2EC0CB',
                    },
                  },
                }}
              />

              {/* File Attachments */}
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 600, color: '#333', mb: 2 }}>
                  Attachments (Optional)
                </Typography>
                <Box
                  sx={{
                    border: '2px dashed #E0E7FF',
                    borderRadius: '12px',
                    padding: '24px',
                    textAlign: 'center',
                    backgroundColor: '#fafbff',
                    transition: 'all 0.3s ease',
                    cursor: 'pointer',
                    '&:hover': {
                      borderColor: '#2EC0CB',
                      backgroundColor: '#f0fdfe'
                    }
                  }}
                  onClick={() => document.getElementById('file-upload').click()}
                >
                  <input
                    id="file-upload"
                    type="file"
                    multiple
                    style={{ display: 'none' }}
                    onChange={(e) => {
                      const files = Array.from(e.target.files);
                      setContactForm(prev => ({
                        ...prev,
                        attachments: [...prev.attachments, ...files]
                      }));
                    }}
                  />
                  <CloudUpload sx={{ fontSize: 48, color: '#2EC0CB', mb: 1 }} />
                  <Typography variant="body1" sx={{ fontWeight: 600, color: '#333', mb: 0.5 }}>
                    Drop files here or click to browse
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    Supported formats: PDF, DOC, DOCX, JPG, PNG (Max 10MB each)
                  </Typography>
                </Box>

                {/* Display uploaded files */}
                {contactForm.attachments.length > 0 && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2" sx={{ fontWeight: 600, color: '#333', mb: 1 }}>
                      Uploaded Files ({contactForm.attachments.length})
                    </Typography>
                    {contactForm.attachments.map((file, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          padding: '8px 12px',
                          backgroundColor: '#E0F7F9',
                          borderRadius: '8px',
                          mb: 1
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <AttachFile sx={{ color: '#2EC0CB', mr: 1, fontSize: 20 }} />
                          <Typography variant="body2" sx={{ color: '#333' }}>
                            {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                          </Typography>
                        </Box>
                        <IconButton
                          size="small"
                          onClick={() => {
                            setContactForm(prev => ({
                              ...prev,
                              attachments: prev.attachments.filter((_, i) => i !== index)
                            }));
                          }}
                          sx={{ color: '#666' }}
                        >
                          <Delete fontSize="small" />
                        </IconButton>
                      </Box>
                    ))}
                  </Box>
                )}
              </Box>
            </Box>
          </DialogContent>

          <DialogActions sx={{
            p: "24px 32px",
            backgroundColor: "#f8fdfe",
            borderTop: "1px solid #E0F7F9",
            gap: 2
          }}>
            <Button
              onClick={() => setContactUsOpen(false)}
              variant="outlined"
              sx={{
                color: '#666',
                borderColor: '#ddd',
                fontWeight: 600,
                borderRadius: '12px',
                padding: '12px 24px',
                textTransform: 'none',
                '&:hover': {
                  backgroundColor: '#f5f5f5',
                  borderColor: '#ccc'
                }
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (!contactForm.email || !contactForm.phone || contactForm.subject.length === 0) {
                  alert("Please fill in all required fields (Email, Phone Number, Subject)");
                  return;
                }
                if (contactForm.subject.includes("Other") && !contactForm.description.trim()) {
                  alert("Description is required when 'Other' is selected");
                  return;
                }
                alert("✅ Ticket Raised Successfully! We'll get back to you soon.");
                setContactUsOpen(false);
                setContactForm({
                  name: "",
                  email: "",
                  phone: "",
                  countryCode: "+1",
                  subject: [],
                  description: "",
                  attachments: []
                });
              }}
              variant="contained"
              sx={{
                background: "linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)",
                fontWeight: 700,
                borderRadius: '12px',
                padding: '12px 32px',
                textTransform: 'none',
                fontSize: '1rem',
                boxShadow: '0 4px 20px rgba(46, 192, 203, 0.3)',
                '&:hover': {
                  background: "linear-gradient(135deg, #23A3AD 0%, #2EC0CB 100%)",
                  boxShadow: '0 6px 25px rgba(46, 192, 203, 0.4)',
                  transform: 'translateY(-1px)'
                },
                transition: 'all 0.3s ease'
              }}
            >
              🎫 Raise Support Ticket
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    </AuthLayout>
  );
};

export default Login;
