import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { motion } from "framer-motion";
import {
  Typography,
  Box,
  Paper,
  Button,
} from "@mui/material";
import AuthLayout from "../../components/Login/AuthLayout";
import ContactUsModal from "../../components/Login/ContactUsModal";

const OTPVerification = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const recipient = location.state?.recipient || "your number/email";

  const [otp, setOtp] = useState(new Array(6).fill(""));
  const [timer, setTimer] = useState(60);
  const [contactUsOpen, setContactUsOpen] = useState(false);
  const inputRefs = useRef([]);

  useEffect(() => {
    inputRefs.current[0]?.focus();
  }, []);

  useEffect(() => {
    if (timer === 0) return;
    const countdown = setInterval(() => {
      setTimer((prev) => prev - 1);
    }, 1000);
    return () => clearInterval(countdown);
  }, [timer]);

  const handleChange = (el, idx) => {
    const val = el.value.replace(/[^0-9]/g, "");
    if (!val) return;
    const newOtp = [...otp];
    newOtp[idx] = val;
    setOtp(newOtp);
    if (idx < 5) inputRefs.current[idx + 1]?.focus();
  };

  const handleBackspace = (e, idx) => {
    if (e.key === "Backspace" && !otp[idx] && idx > 0) {
      inputRefs.current[idx - 1]?.focus();
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const code = otp.join("");
    if (code.length === 6) {
      console.log("Verifying OTP:", code);
      navigate("/reset-password");
    } else {
      alert("Enter 6-digit OTP");
    }
  };

  const handleResend = () => {
    setOtp(new Array(6).fill(""));
    setTimer(60);
    inputRefs.current[0]?.focus();
    alert("OTP resent to " + recipient);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to right, #2EC0CB, #23A3AD)",
        padding: 24,
      }}
    >
      <Paper
        elevation={6}
        component="form"
        onSubmit={handleSubmit}
        sx={{
          p: 5,
          borderRadius: 6,
          maxWidth: 400,
          width: "100%",
          border: "2px solid #23A3AD",
        }}
      >
        <Typography
          variant="h6"
          fontWeight="bold"
          align="center"
          sx={{ color: "#2EC0CB", mb: 1 }}
        >
          Enter OTP sent to
        </Typography>

        <Typography
          variant="body2"
          align="center"
          sx={{ color: "#777", mb: 3 }}
        >
          {recipient}
        </Typography>

        <Box display="flex" justifyContent="center" gap={1.5} mb={2}>
          {otp.map((digit, i) => (
            <input
              key={i}
              ref={(el) => (inputRefs.current[i] = el)}
              type="text"
              maxLength="1"
              value={digit}
              onChange={(e) => handleChange(e.target, i)}
              onKeyDown={(e) => handleBackspace(e, i)}
              style={{
                width: 40,
                height: 50,
                fontSize: "1.25rem",
                textAlign: "center",
                border: "1px solid #ccc",
                borderRadius: "8px",
                outline: "none",
                boxShadow: "inset 0 0 3px rgba(0,0,0,0.1)",
              }}
            />
          ))}
        </Box>

        <Box textAlign="center" sx={{ mb: 2 }}>
          {timer > 0 ? (
            <Typography variant="body2" sx={{ color: "#666" }}>
              Resend OTP in <strong>{timer}s</strong>
            </Typography>
          ) : (
            <Button
              variant="text"
              onClick={handleResend}
              sx={{ color: "#2EC0CB", textTransform: "none", fontWeight: "medium" }}
            >
              Resend OTP
            </Button>
          )}
        </Box>

        <Button
          type="submit"
          variant="contained"
          fullWidth
          sx={{
            background: "linear-gradient(to right, #2EC0CB, #23A3AD)",
            fontWeight: "bold",
            py: 1.3,
            fontSize: "1rem",
            borderRadius: "12px",
            textTransform: "none",
          }}
        >
          Verify & Continue
        </Button>

        <Typography align="center" sx={{ mt: 2, fontSize: "0.8rem" }}>
          Having issues?{" "}
          <span
            onClick={() => setContactUsOpen(true)}
            style={{
              color: "#2EC0CB",
              fontWeight: 600,
              textDecoration: "none",
              cursor: "pointer"
            }}
          >
            Contact Us
          </span>
          {" | "}
          <Link to="/login" style={{ color: "#2EC0CB", fontWeight: 600 }}>
            Login
          </Link>
        </Typography>
      </Paper>

      <ContactUsModal
        open={contactUsOpen}
        onClose={() => setContactUsOpen(false)}
      />
    </motion.div>
  );
};

export default OTPVerification;
