// src/components/FullDetailCard.jsx
import React from 'react';
import { VisibilityOff, CheckCircle, Schedule, Unarchive, Archive, Star, StarBorder, Delete } from '@mui/icons-material';
import { getNotificationIconAndColor, getPriorityBadgeClass, formatRelativeTime } from '../Notification/helpers';

const FullDetailCard = React.memo(({ notification, onAction }) => {
    const { id, type, status, archived, starred, priority, title, message, details, timestamp, link } = notification;
    const { icon, color } = getNotificationIconAndColor(type);
    const priorityClass = getPriorityBadgeClass(priority);

    const statusBadge = archived ? (
        <span className="text-xs font-semibold text-gray-500 px-2 py-0.5 rounded-full ml-2" style={{ backgroundColor: 'var(--color-gray-200)' }}>Archived</span>
    ) : (
        status === 'unread' ? (
            <span className="text-xs font-semibold text-red-700 px-2 py-0.5 rounded-full ml-2" style={{ backgroundColor: 'var(--color-red-100)' }}>New</span>
        ) : (
            status === 'remind_later' ? (
                <span className="text-xs font-semibold text-amber-700 px-2 py-0.5 rounded-full ml-2" style={{ backgroundColor: 'var(--color-amber-100)' }}>Reminded</span>
            ) : null
        )
    );

    const markButton = (status === 'read' || status === 'remind_later') ? (
        <button className="full-detail-action-btn mark-unread-btn" onClick={(e) => onAction(id, 'mark-unread', e.currentTarget)} title="Mark as Unread">
            <VisibilityOff fontSize="small" /> Unread
        </button>
    ) : (
        status === 'unread' ? (
            <button className="full-detail-action-btn mark-read-btn" onClick={(e) => onAction(id, 'mark-read', e.currentTarget)} title="Mark as Read">
                <CheckCircle fontSize="small" /> Read
            </button>
        ) : null
    );

    const remindButton = !archived && status !== 'remind_later' ? (
        <button className="full-detail-action-btn remind-later-btn" onClick={(e) => onAction(id, 'remind-later', e.currentTarget)} title="Remind Later">
            <Schedule fontSize="small" /> Remind
        </button>
    ) : null;

    const archiveButton = archived ? (
        <button className="full-detail-action-btn archive-btn" onClick={(e) => onAction(id, 'unarchive', e.currentTarget)} title="Unarchive">
            <Unarchive fontSize="small" /> Unarchive
        </button>
    ) : (
        <button className="full-detail-action-btn archive-btn" onClick={(e) => onAction(id, 'archive', e.currentTarget)} title="Archive">
            <Archive fontSize="small" /> Archive
        </button>
    );

    const starButtonClass = starred ? 'active' : '';
    const starButtonIcon = starred ? <Star fontSize="small" color="warning" /> : <StarBorder fontSize="small" color="action" />;
    const starButton = (
        <button className={`full-detail-action-btn star-btn ${starButtonClass}`} onClick={(e) => onAction(id, 'toggle-star', e.currentTarget)} title="Toggle Star">
            {starButtonIcon} Star
        </button>
    );

    const deleteButton = (
        <button className="full-detail-action-btn delete-btn" onClick={(e) => onAction(id, 'delete', e.currentTarget)} title="Delete Notification">
            <Delete fontSize="small" /> Delete
        </button>
    );

    return (
        <div
            className={`full-detail-card border-${type} ${status === 'unread' ? 'unread' : ''} ${archived ? 'archived' : ''} ${status === 'remind_later' ? 'remind-later' : ''} ${starred ? 'starred' : ''}`}
            data-id={id}
        >
            <div className="flex items-start w-full">
                {icon && React.createElement(icon, { className: `${color} text-2xl w-8 text-center`, style: { marginRight: '1rem' } })}
                <div className="flex-1">
                    <div className="flex justify-between items-center mb-1">
                        <h3 className="font-semibold text-lg text-gray-900">
                            {title}
                            {priority && <span className={`priority-badge ${priorityClass}`}>{priority}</span>}
                            {statusBadge}
                        </h3>
                        <p className="text-gray-700 mb-2">{message}</p>
                        {details && <p className="text-gray-600 text-sm mb-2">{details}</p>}
                        <a href={link} className="text-blue-600 hover:underline text-sm font-medium" onClick={(e) => e.stopPropagation()}>View Details</a>
                    </div>
                </div>
                <div className="flex flex-col gap-1 ml-4 justify-center">
                    {starButton}
                    {markButton}
                    {remindButton}
                    {archiveButton}
                    {deleteButton}
                </div>
            </div>
        </div>
    );
});

export default FullDetailCard;