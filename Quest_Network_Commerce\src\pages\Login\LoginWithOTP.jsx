import React from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import {
  Box,
  Button,
  TextField,
  Typography,
  InputAdornment,
  Paper,
} from "@mui/material";
import { motion } from "framer-motion";
import AuthLayout from "../../components/Login/AuthLayout";
import StyledTextField from "../../components/Login/StyledTextField";

const LoginWithOTP = () => {
  const { register, handleSubmit, formState: { errors } } = useForm();
  const navigate = useNavigate();

  const onSubmit = (data) => {
    console.log("Phone number submitted:", data.phone);
    navigate("/verify-otp", { state: { phone: data.phone } });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 40 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to right, #2EC0CB, #23A3AD)",
        padding: 24,
      }}
    >
      <Paper
        elevation={6}
        sx={{
          p: 5,
          borderRadius: 6,
          maxWidth: 400,
          width: "100%",
          border: "2px solid #23A3AD",
        }}
      >
        <Typography
          variant="h5"
          align="center"
          fontWeight="bold"
          sx={{ color: "#2EC0CB", mb: 3 }}
        >
          Login with OTP
        </Typography>

        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          sx={{ display: "flex", flexDirection: "column", gap: 3 }}
        >
          <TextField
            label="Phone Number"
            type="tel"
            variant="outlined"
            fullWidth
            placeholder="Enter Indian phone number"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">+91</InputAdornment>
              ),
              sx: {
                backgroundColor: "#f9f9f9",
                borderRadius: "12px",
                fontWeight: 500,
              },
            }}
            InputLabelProps={{
              sx: {
                color: "#23A3AD",
                "&.Mui-focused": {
                  color: "#2EC0CB",
                },
              },
            }}
            error={!!errors.phone}
            helperText={errors.phone?.message}
            {...register("phone", {
              required: "Phone number is required",
              pattern: {
                value: /^[6-9]\d{9}$/,
                message: "Enter a valid Indian phone number",
              },
            })}
          />

          <Button
            variant="contained"
            type="submit"
            fullWidth
            sx={{
              background: "linear-gradient(to right, #2EC0CB, #23A3AD)",
              fontWeight: "bold",
              py: 1.5,
              fontSize: "1rem",
              borderRadius: "12px",
              textTransform: "none",
              "&:hover": {
                background: "#23A3AD",
              },
            }}
          >
            Send OTP
          </Button>
        </Box>
      </Paper>
    </motion.div>
  );
};

export default LoginWithOTP;
