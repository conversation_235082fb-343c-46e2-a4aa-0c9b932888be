import React from "react";
import { Box, Typography, Stack, IconButton } from "@mui/material";

const socialIcons = [
  { src: "/icons/google.png", alt: "Google" },
  { src: "/icons/facebook.png", alt: "Facebook" },
  { src: "/icons/apple.png", alt: "Apple" },
  { src: "/icons/twitter.png", alt: "Twitter" },
  { src: "/icons/microsoft.png", alt: "Microsoft" },
];

const SocialLoginButtons = () => {
  return (
    <>
      <Typography variant="body2" align="center" color="text.secondary" mt={6}>
        Or sign in with
      </Typography>
      <Stack direction="row" justifyContent="center" spacing={3} mt={3}>
        {socialIcons.map(({ src, alt }) => (
          <IconButton
            key={alt}
            sx={{
              transition: "transform 0.3s ease",
              "&:hover": {
                transform: "scale(1.1)",
              },
            }}
          >
            <Box
              component="img"
              src={src}
              alt={alt}
              sx={{ width: 40, height: 40 }}
            />
          </IconButton>
        ))}
      </Stack>
    </>
  );
};

export default SocialLoginButtons;
