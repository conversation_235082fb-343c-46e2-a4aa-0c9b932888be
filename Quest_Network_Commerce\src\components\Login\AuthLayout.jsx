import { Typography } from "@mui/material";
import hclLogo from "../../assets/images/HCLSoftwareLogoWhite.png";

const AuthLayout = ({ children, title, subtitle }) => {
  return (
    <div
      style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)',
        padding: '24px'
      }}
    >
      <div
        style={{
          background: 'white',
          borderRadius: '24px',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          overflow: 'hidden',
          width: '100%',
          maxWidth: '1200px',
          border: '1px solid #23A3AD',
          display: 'flex',
          flexDirection: 'row',
          minHeight: '600px'
        }}
      >
        {/* Left Side - Branding */}
        <div
          style={{
            background: 'linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)',
            width: '50%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '48px',
            color: 'white',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {/* Content */}
          <div style={{ textAlign: 'center', zIndex: 10, position: 'relative' }}>
            {/* HCL Logo */}
            <div style={{ marginBottom: '40px' }}>
              <img
                src={hclLogo}
                alt="HCL Software"
                style={{
                  maxWidth: '380px',
                  height: 'auto',
                  filter: 'drop-shadow(0 6px 16px rgba(0,0,0,0.4))',
                  transform: 'scale(1.1)',
                }}
              />
            </div>

            {/* Divider */}
            <div style={{
              width: '96px',
              height: '2px',
              background: 'rgba(255,255,255,0.6)',
              margin: '0 auto 24px auto'
            }} />

            {/* Subtitle */}
            <div style={{ marginBottom: '24px' }}>
              <Typography
                variant="h4"
                sx={{
                  fontSize: { xs: "1.4rem", md: "1.8rem" },
                  fontWeight: 600,
                  letterSpacing: "0.06em",
                  color: "rgba(255,255,255,1)",
                  fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
                  textTransform: "uppercase",
                  lineHeight: 1.4,
                  textShadow: "0 2px 8px rgba(0,0,0,0.3)",
                }}
              >
                <div style={{ fontWeight: 700 }}>Quest Network</div>
                <div style={{ fontWeight: 500, marginTop: '4px' }}>Commerce</div>
              </Typography>
            </div>


          </div>
        </div>

        {/* Right Side - Form Content */}
        <div
          style={{
            width: '50%',
            padding: '40px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            background: 'white'
          }}
        >
          <div style={{ maxWidth: '400px', margin: '0 auto', width: '100%' }}>
            {title && (
              <div style={{ textAlign: 'center', marginBottom: '32px' }}>
                <Typography
                  variant="h4"
                  sx={{
                    fontSize: { xs: "1.75rem", md: "2rem" },
                    fontWeight: 'bold',
                    color: '#2EC0CB',
                    marginBottom: '8px',
                    fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif"
                  }}
                >
                  {title}
                </Typography>
                {subtitle && (
                  <Typography
                    variant="body1"
                    sx={{
                      fontSize: "1rem",
                      color: '#666',
                      fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    }}
                  >
                    {subtitle}
                  </Typography>
                )}
              </div>
            )}

            <div>
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
