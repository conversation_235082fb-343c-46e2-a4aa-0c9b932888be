import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Box,
  Card,
  CardContent,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  FormControl,
  Select,
  MenuItem,
  Chip,
  TextField
} from "@mui/material";
import {
  PhoneAndroid,
  Email,
  Security,
  CheckCircle,
  Close,
  CloudUpload,
  AttachFile,
  Delete
} from "@mui/icons-material";
import AuthLayout from "../../components/Login/AuthLayout";
import StyledTextField from "../../components/Login/StyledTextField";



const MFAVerify = () => {
  const [otp, setOtp] = useState(new Array(6).fill(""));
  const [method, setMethod] = useState("authenticator");
  const [contactUsOpen, setContactUsOpen] = useState(false);
  const [contactForm, setContactForm] = useState({
    name: "",
    email: "",
    phone: "",
    countryCode: "+1",
    subject: [],
    description: "",
    attachments: []
  });


  const inputRefs = useRef([]);
  const navigate = useNavigate();

  // Country codes for phone number
  const countryCodes = [
    { code: "+1", country: "US", name: "United States" },
    { code: "+44", country: "UK", name: "United Kingdom" },
    { code: "+91", country: "IN", name: "India" },
    { code: "+86", country: "CN", name: "China" },
    { code: "+81", country: "JP", name: "Japan" },
    { code: "+49", country: "DE", name: "Germany" },
    { code: "+33", country: "FR", name: "France" },
    { code: "+39", country: "IT", name: "Italy" },
    { code: "+34", country: "ES", name: "Spain" },
    { code: "+7", country: "RU", name: "Russia" },
    { code: "+55", country: "BR", name: "Brazil" },
    { code: "+52", country: "MX", name: "Mexico" },
    { code: "+61", country: "AU", name: "Australia" },
    { code: "+82", country: "KR", name: "South Korea" },
    { code: "+65", country: "SG", name: "Singapore" },
    { code: "+60", country: "MY", name: "Malaysia" },
    { code: "+66", country: "TH", name: "Thailand" },
    { code: "+84", country: "VN", name: "Vietnam" },
    { code: "+63", country: "PH", name: "Philippines" },
    { code: "+62", country: "ID", name: "Indonesia" },
    { code: "+971", country: "AE", name: "UAE" },
    { code: "+966", country: "SA", name: "Saudi Arabia" },
    { code: "+972", country: "IL", name: "Israel" },
    { code: "+90", country: "TR", name: "Turkey" },
    { code: "+27", country: "ZA", name: "South Africa" }
  ];

  useEffect(() => {
    inputRefs.current[0]?.focus();
  }, []);

  const handleChange = (el, idx) => {
    const val = el.value.replace(/[^0-9]/g, "");
    if (!val) return;
    const newOtp = [...otp];
    newOtp[idx] = val;
    setOtp(newOtp);
    if (idx < 5) inputRefs.current[idx + 1]?.focus();
  };

  const handleBackspace = (e, idx) => {
    if (e.key === "Backspace" && !otp[idx] && idx > 0) {
      inputRefs.current[idx - 1]?.focus();
    }
  };

  const handleVerify = (e) => {
    e.preventDefault();
    const code = otp.join("");
    if (code.length === 6) {
      alert(`✅ MFA Verified using ${method}!`);
      navigate("/home");
    } else {
      alert("❌ Enter all 6 digits.");
    }
  };

  const handleResend = () => {
    setOtp(new Array(6).fill(""));
    inputRefs.current[0]?.focus();

    if (method === "sms") {
      alert("OTP resent via SMS.");
    } else if (method === "email") {
      alert("OTP resent to Email.");
    }
  };

  return (
    <AuthLayout title="Multi-Factor Authentication" subtitle="Enter the 6-digit code to verify your identity">
      <Box sx={{ maxWidth: '320px', margin: '0 auto', width: '100%' }}>
        <form onSubmit={handleVerify} style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>

        {/* Compact Method Selection */}
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="body1"
            align="center"
            sx={{
              mb: 2,
              color: "#333",
              fontWeight: 600,
              fontSize: '0.95rem',
              fontFamily: "'HCLTechRoobert', sans-serif"
            }}
          >
            Choose verification method
          </Typography>

          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: 0.8,
            flexWrap: 'nowrap',
            maxWidth: '280px',
            margin: '0 auto'
          }}>
            {[
              {
                value: "authenticator",
                label: "Authenticator",
                subtitle: "App",
                icon: <Security sx={{ fontSize: 20 }} />,
                color: "#2EC0CB"
              },
              {
                value: "sms",
                label: "SMS",
                subtitle: "Text Message",
                icon: <PhoneAndroid sx={{ fontSize: 20 }} />,
                color: "#23A3AD"
              },
              {
                value: "email",
                label: "Email",
                subtitle: "Email Code",
                icon: <Email sx={{ fontSize: 20 }} />,
                color: "#2EC0CB"
              },
            ].map((item) => (
              <Card
                key={item.value}
                onClick={() => setMethod(item.value)}
                sx={{
                  cursor: 'pointer',
                  position: 'relative',
                  borderRadius: '12px',
                  border: method === item.value ? `2px solid ${item.color}` : '1px solid #E0E7FF',
                  backgroundColor: method === item.value ? '#F0FDFE' : '#FFFFFF',
                  boxShadow: method === item.value
                    ? `0 4px 12px rgba(46, 192, 203, 0.15)`
                    : '0 1px 4px rgba(0,0,0,0.05)',
                  transition: 'all 0.2s ease',
                  transform: method === item.value ? 'translateY(-1px)' : 'translateY(0)',
                  minWidth: '70px',
                  maxWidth: '85px',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: method === item.value
                      ? `0 6px 16px rgba(46, 192, 203, 0.2)`
                      : '0 4px 12px rgba(0,0,0,0.1)',
                    borderColor: item.color
                  }
                }}
              >
                <CardContent sx={{
                  textAlign: 'center',
                  py: 1,
                  px: 0.5,
                  position: 'relative',
                  '&:last-child': { pb: 1 }
                }}>
                  {/* Selection Indicator */}
                  {method === item.value && (
                    <CheckCircle
                      sx={{
                        position: 'absolute',
                        top: 4,
                        right: 4,
                        color: item.color,
                        fontSize: 16
                      }}
                    />
                  )}

                  {/* Icon */}
                  <Avatar sx={{
                    bgcolor: method === item.value ? item.color : '#F5F7FA',
                    color: method === item.value ? 'white' : '#666',
                    width: 28,
                    height: 28,
                    margin: '0 auto 6px auto',
                    transition: 'all 0.2s ease'
                  }}>
                    {React.cloneElement(item.icon, { sx: { fontSize: 16 } })}
                  </Avatar>

                  {/* Label */}
                  <Typography
                    variant="caption"
                    sx={{
                      fontWeight: 600,
                      color: method === item.value ? item.color : '#333',
                      fontSize: '0.7rem',
                      lineHeight: 1.2,
                      fontFamily: "'HCLTechRoobert', sans-serif"
                    }}
                  >
                    {item.label}
                  </Typography>

                  {/* Subtitle */}
                  <Typography
                    variant="caption"
                    sx={{
                      color: '#666',
                      fontSize: '0.6rem',
                      fontWeight: 400,
                      lineHeight: 1
                    }}
                  >
                    {item.subtitle}
                  </Typography>
                </CardContent>
              </Card>
            ))}
          </Box>
        </Box>

        {/* Compact OTP Input Section */}
        <Box sx={{ textAlign: 'center', mb: 2 }}>
          <Typography
            variant="body2"
            sx={{
              color: "#555",
              fontSize: '0.9rem',
              fontWeight: 500,
              mb: 0.5,
              fontFamily: "'HCLTechRoobert', sans-serif"
            }}
          >
            Enter 6-digit code
          </Typography>

          <Typography
            variant="caption"
            sx={{
              color: "#888",
              fontSize: '0.75rem',
              mb: 3
            }}
          >
            Code sent to your {method === "authenticator" ? "Authenticator App" : method === "sms" ? "phone" : "email"}
          </Typography>

          {/* Enhanced OTP Input Grid */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            gap: { xs: 0.5, sm: 0.8 },
            mb: 2,
            flexWrap: 'wrap'
          }}>
            {otp.map((digit, i) => (
              <Box
                key={i}
                sx={{
                  position: 'relative',
                  '&::after': digit ? {
                    content: '""',
                    position: 'absolute',
                    bottom: -8,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    width: 6,
                    height: 6,
                    borderRadius: '50%',
                    backgroundColor: '#2EC0CB',
                    opacity: 0.7
                  } : {}
                }}
              >
                <input
                  ref={(el) => (inputRefs.current[i] = el)}
                  type="text"
                  maxLength="1"
                  value={digit}
                  onChange={(e) => handleChange(e.target, i)}
                  onKeyDown={(e) => handleBackspace(e, i)}
                  style={{
                    width: '40px',
                    height: '50px',
                    fontSize: '1.5rem',
                    fontWeight: '700',
                    textAlign: 'center',
                    border: digit ? '2px solid #2EC0CB' : '1px solid #E0E7FF',
                    borderRadius: '12px',
                    outline: 'none',
                    backgroundColor: digit ? '#F0FDFE' : '#FAFBFF',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    fontFamily: "'HCLTechRoobert', sans-serif",
                    color: '#333',
                    boxShadow: digit
                      ? '0 4px 20px rgba(46, 192, 203, 0.15)'
                      : '0 2px 8px rgba(0,0,0,0.05)'
                  }}
                  onFocus={(e) => {
                    e.target.style.borderColor = '#2EC0CB';
                    e.target.style.backgroundColor = '#F0FDFE';
                    e.target.style.boxShadow = '0 0 0 4px rgba(46, 192, 203, 0.1)';
                    e.target.style.transform = 'scale(1.05)';
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = digit ? '#2EC0CB' : '#E0E7FF';
                    e.target.style.backgroundColor = digit ? '#F0FDFE' : '#FAFBFF';
                    e.target.style.boxShadow = digit
                      ? '0 4px 20px rgba(46, 192, 203, 0.15)'
                      : '0 2px 8px rgba(0,0,0,0.05)';
                    e.target.style.transform = 'scale(1)';
                  }}
                />
              </Box>
            ))}
          </Box>
        </Box>

        {method !== "authenticator" && (
          <Typography
            variant="body2"
            align="center"
            sx={{ mb: 2, color: "#666" }}
          >
            Didn’t get it?{" "}
            <span
              onClick={handleResend}
              style={{
                color: "#2EC0CB",
                textDecoration: "none",
                cursor: "pointer",
                fontWeight: 600,
              }}
            >
              Resend Code
            </span>
          </Typography>
        )}

        <Button
          type="submit"
          variant="contained"
          fullWidth
          sx={{
            background: "linear-gradient(to right, #2EC0CB, #23A3AD)",
            fontWeight: "bold",
            py: 1,
            fontSize: "0.9rem",
            borderRadius: "12px",
            textTransform: "none",
            mt: 1,
          }}
        >
          Verify
        </Button>

        <Typography
          variant="caption"
          align="center"
          display="block"
          sx={{ mt: 2, color: "#888", fontSize: '0.75rem' }}
        >
          Trouble logging in?{" "}
          <span
            style={{
              color: "#2EC0CB",
             
              cursor: "pointer",
            }}
            onClick={() => setContactUsOpen(true)}
          >
            Contact Us
          </span>{" "}
          |{" "}
          <span
            style={{
              color: "#2EC0CB",
              
              cursor: "pointer",
            }}
            onClick={() => navigate("/login")}
          >
            Login
          </span>
        </Typography>




      </form>
      </Box>

        {/* Contact Us Modal - Same as Login Page */}
        <Dialog
          open={contactUsOpen}
          onClose={() => setContactUsOpen(false)}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: "20px",
              maxHeight: "95vh",
              background: "linear-gradient(135deg, #ffffff 0%, #f8fdfe 100%)",
              boxShadow: "0 20px 60px rgba(46, 192, 203, 0.15)",
              overflow: "hidden"
            }
          }}
        >
          <DialogTitle sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            background: "linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)",
            color: "white",
            fontFamily: "'HCLTechRoobert', sans-serif",
            padding: "24px 32px",
            borderBottom: "none"
          }}>
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 700, mb: 0.5 }}>
                Contact Support
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9, fontWeight: 400 }}>
                We're here to help you with any login or account issues
              </Typography>
            </Box>
            <IconButton
              onClick={() => setContactUsOpen(false)}
              sx={{
                color: "white",
                backgroundColor: "rgba(255,255,255,0.1)",
                "&:hover": {
                  backgroundColor: "rgba(255,255,255,0.2)"
                }
              }}
            >
              <Close />
            </IconButton>
          </DialogTitle>

          <DialogContent sx={{ p: "32px", pt: "24px", backgroundColor: "transparent" }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 2 }}>
              <StyledTextField
                label="Full Name (Optional)"
                value={contactForm.name}
                onChange={(e) => setContactForm(prev => ({ ...prev, name: e.target.value }))}
                fullWidth
                placeholder="Enter your full name"
                validation={{
                  maxLength: 50
                }}
              />

              <StyledTextField
                label="Email Address *"
                type="email"
                value={contactForm.email}
                onChange={(e) => setContactForm(prev => ({ ...prev, email: e.target.value }))}
                fullWidth
                required
                placeholder="Enter your email address"
                validation={{
                  required: true,
                  email: true
                }}
              />

              <StyledTextField
                label="Phone Number *"
                value={contactForm.phone}
                onChange={(e) => setContactForm(prev => ({ ...prev, phone: e.target.value }))}
                fullWidth
                required
                placeholder="Enter your phone number"
                validation={{
                  required: true,
                  phone: true
                }}
              />

              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 600, color: '#333' }}>
                    Subject *
                  </Typography>
                  {contactForm.subject.length > 0 && (
                    <Button
                      size="small"
                      onClick={() => setContactForm(prev => ({ ...prev, subject: [] }))}
                      sx={{
                        color: '#2EC0CB',
                        fontSize: '0.75rem',
                        textTransform: 'none',
                        minWidth: 'auto',
                        padding: '2px 8px',
                        '&:hover': {
                          backgroundColor: '#E0F7F9'
                        }
                      }}
                    >
                      Clear All
                    </Button>
                  )}
                </Box>
                <FormControl fullWidth required>
                  <Select
                    multiple
                    value={contactForm.subject}
                    onChange={(e) => setContactForm(prev => ({ ...prev, subject: e.target.value }))}
                    displayEmpty
                    onClose={(e) => e.stopPropagation()}
                    renderValue={(selected) => {
                      if (selected.length === 0) {
                        return <Typography sx={{ color: '#999' }}>Select issue categories</Typography>;
                      }
                      return (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip
                              key={value}
                              label={value}
                              size="small"
                              onDelete={(e) => {
                                e.stopPropagation();
                                setContactForm(prev => ({
                                  ...prev,
                                  subject: prev.subject.filter(s => s !== value)
                                }));
                              }}
                              onMouseDown={(e) => e.stopPropagation()}
                              sx={{
                                backgroundColor: '#E0F7F9',
                                color: '#2EC0CB',
                                fontWeight: 500,
                                '& .MuiChip-deleteIcon': {
                                  color: '#2EC0CB',
                                  '&:hover': {
                                    color: '#23A3AD'
                                  }
                                }
                              }}
                            />
                          ))}
                        </Box>
                      );
                    }}
                    sx={{
                      borderRadius: '12px',
                      backgroundColor: '#ffffff',
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#E0E7FF',
                        borderWidth: '2px',
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#2EC0CB',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#2EC0CB',
                        borderWidth: '2px',
                      },
                      '& .MuiSelect-select': {
                        padding: '16px 14px',
                        minHeight: '24px',
                      }
                    }}
                  >
                    {[
                      "Login Issues",
                      "Password Reset Problems",
                      "Account Access Denied",
                      "Two-Factor Authentication Issues",
                      "Email Verification Problems",
                      "Account Locked/Suspended",
                      "Username/Email Not Recognized",
                      "Social Media Login Issues",
                      "Browser Compatibility Issues",
                      "Mobile App Login Problems",
                      "Other"
                    ].map((option) => (
                      <MenuItem
                        key={option}
                        value={option}
                        sx={{
                          '&:hover': {
                            backgroundColor: '#E0F7F9'
                          },
                          '&.Mui-selected': {
                            backgroundColor: '#E0F7F9',
                            '&:hover': {
                              backgroundColor: '#B8F0F3'
                            }
                          }
                        }}
                      >
                        {option}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>

              <TextField
                label={contactForm.subject.includes("Other") ? "Description *" : "Description (Optional)"}
                multiline
                rows={4}
                value={contactForm.description}
                onChange={(e) => setContactForm(prev => ({ ...prev, description: e.target.value }))}
                fullWidth
                required={contactForm.subject.includes("Other")}
                placeholder="Please describe your issue in detail..."
                variant="outlined"
                sx={{
                  borderRadius: '12px',
                  backgroundColor: '#ffffff',
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '12px',
                    '& fieldset': {
                      borderColor: '#E0E7FF',
                      borderWidth: '2px',
                    },
                    '&:hover fieldset': {
                      borderColor: '#2EC0CB',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#2EC0CB',
                      borderWidth: '2px',
                    },
                    '& textarea': {
                      color: '#333',
                      fontSize: '16px',
                      lineHeight: '1.5',
                      '&::placeholder': {
                        color: '#999',
                        opacity: 1,
                      }
                    }
                  },
                  '& .MuiInputLabel-root': {
                    color: '#666',
                    fontSize: '16px',
                    '&.Mui-focused': {
                      color: '#2EC0CB',
                    },
                  },
                }}
              />

              {/* File Attachments */}
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 600, color: '#333', mb: 2 }}>
                  Attachments (Optional)
                </Typography>
                <Box
                  sx={{
                    border: '2px dashed #E0E7FF',
                    borderRadius: '12px',
                    padding: '24px',
                    textAlign: 'center',
                    backgroundColor: '#fafbff',
                    transition: 'all 0.3s ease',
                    cursor: 'pointer',
                    '&:hover': {
                      borderColor: '#2EC0CB',
                      backgroundColor: '#f0fdfe'
                    }
                  }}
                  onClick={() => document.getElementById('file-upload-mfa').click()}
                >
                  <input
                    id="file-upload-mfa"
                    type="file"
                    multiple
                    style={{ display: 'none' }}
                    onChange={(e) => {
                      const files = Array.from(e.target.files);
                      setContactForm(prev => ({
                        ...prev,
                        attachments: [...prev.attachments, ...files]
                      }));
                    }}
                  />
                  <CloudUpload sx={{ fontSize: 48, color: '#2EC0CB', mb: 1 }} />
                  <Typography variant="body1" sx={{ fontWeight: 600, color: '#333', mb: 0.5 }}>
                    Drop files here or click to browse
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    Supported formats: PDF, DOC, DOCX, JPG, PNG (Max 10MB each)
                  </Typography>
                </Box>

                {/* Display uploaded files */}
                {contactForm.attachments.length > 0 && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2" sx={{ fontWeight: 600, color: '#333', mb: 1 }}>
                      Uploaded Files ({contactForm.attachments.length})
                    </Typography>
                    {contactForm.attachments.map((file, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          padding: '8px 12px',
                          backgroundColor: '#E0F7F9',
                          borderRadius: '8px',
                          mb: 1
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <AttachFile sx={{ color: '#2EC0CB', mr: 1, fontSize: 20 }} />
                          <Typography variant="body2" sx={{ color: '#333' }}>
                            {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                          </Typography>
                        </Box>
                        <IconButton
                          size="small"
                          onClick={() => {
                            setContactForm(prev => ({
                              ...prev,
                              attachments: prev.attachments.filter((_, i) => i !== index)
                            }));
                          }}
                          sx={{ color: '#666' }}
                        >
                          <Delete fontSize="small" />
                        </IconButton>
                      </Box>
                    ))}
                  </Box>
                )}
              </Box>
            </Box>
          </DialogContent>

          <DialogActions sx={{
            p: "24px 32px",
            backgroundColor: "#f8fdfe",
            borderTop: "1px solid #E0F7F9",
            gap: 2
          }}>
            <Button
              onClick={() => setContactUsOpen(false)}
              variant="outlined"
              sx={{
                color: '#666',
                borderColor: '#ddd',
                fontWeight: 600,
                borderRadius: '12px',
                padding: '12px 24px',
                textTransform: 'none',
                '&:hover': {
                  backgroundColor: '#f5f5f5',
                  borderColor: '#ccc'
                }
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (!contactForm.email || !contactForm.phone || contactForm.subject.length === 0) {
                  alert("Please fill in all required fields (Email, Phone Number, Subject)");
                  return;
                }
                if (contactForm.subject.includes("Other") && !contactForm.description.trim()) {
                  alert("Description is required when 'Other' is selected");
                  return;
                }
                alert("✅ Ticket Raised Successfully! We'll get back to you soon.");
                setContactUsOpen(false);
                setContactForm({
                  name: "",
                  email: "",
                  phone: "",
                  countryCode: "+1",
                  subject: [],
                  description: "",
                  attachments: []
                });
              }}
              variant="contained"
              sx={{
                background: "linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)",
                fontWeight: 700,
                borderRadius: '12px',
                padding: '12px 32px',
                textTransform: 'none',
                fontSize: '1rem',
                boxShadow: '0 4px 20px rgba(46, 192, 203, 0.3)',
                '&:hover': {
                  background: "linear-gradient(135deg, #23A3AD 0%, #2EC0CB 100%)",
                  boxShadow: '0 6px 25px rgba(46, 192, 203, 0.4)',
                  transform: 'translateY(-1px)'
                },
                transition: 'all 0.3s ease'
              }}
            >
              🎫 Raise Support Ticket
            </Button>
          </DialogActions>
        </Dialog>
    </AuthLayout>
  );
};

export default MFAVerify;
