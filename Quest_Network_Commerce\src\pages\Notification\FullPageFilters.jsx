// src/components/FullPageFilters.jsx
import React from 'react';
import { Star, Archive, Schedule } from '@mui/icons-material';
import { getNotificationIconAndColor } from '../Notification/helpers';

const FullPageFilters = React.memo(({
    notifications,
    selectedCategories,
    setSelectedCategories,
    selectedStatuses,
    setSelectedStatuses,
    selectedPriorities,
    setSelectedPriorities,
    currentSearchQuery,
    setCurrentSearchQuery,
    onClearAllFilters
}) => {

    const getCount = (filterType, value) => {
        return notifications.filter(n => {
            if (filterType === 'status') {
                if (value === 'unread') return n.status === 'unread' && !n.archived;
                if (value === 'read') return n.status === 'read' && !n.archived;
                if (value === 'archived') return n.archived;
                if (value === 'remind_later') return n.status === 'remind_later' && !n.archived;
                if (value === 'starred') return n.starred;
            } else if (filterType === 'priority') {
                return n.priority === value;
            } else if (filterType === 'category') {
                return n.type === value;
            }
            return false;
        }).length;
    };

    const handleStatusChange = (e) => {
        const status = e.target.dataset.filterStatus;
        setSelectedStatuses(prev => {
            const newSet = new Set(prev);
            if (e.target.checked) {
                newSet.add(status);
            } else {
                newSet.delete(status);
            }
            if (newSet.size === 0) {
                newSet.add(status);
            }
            return newSet;
        });
    };

    const handlePriorityChange = (e) => {
        const priority = e.target.dataset.filterPriority;
        setSelectedPriorities(prev => {
            const newSet = new Set(prev);
            if (e.target.checked) {
                newSet.add(priority);
            } else {
                newSet.delete(priority);
            }
            if (newSet.size === 0) {
                newSet.add(priority);
            }
            return newSet;
        });
    };

    const handleCategoryChange = (e) => {
        const category = e.target.dataset.filterCategory;
        setSelectedCategories(prev => {
            const newSet = new Set(prev);
            if (e.target.checked) {
                newSet.add(category);
            } else {
                newSet.delete(category);
            }
            if (newSet.size === 0) {
                newSet.add(category);
            }
            return newSet;
        });
    };

    const statusFilters = ['unread', 'read', 'remind_later', 'starred', 'archived'].map(status => {
        let labelText = status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ');
        let iconHtml = null;
        if (status === 'starred') iconHtml = <Star style={{ color: 'var(--color-gold)' }} fontSize="small" />;
        else if (status === 'archived') iconHtml = <Archive fontSize="small" />;
        else if (status === 'remind_later') iconHtml = <Schedule fontSize="small" />;

        return (
            <label key={status} className="flex items-center text-gray-700">
                <input
                    type="checkbox"
                    data-filter-status={status}
                    className="form-checkbox"
                    checked={selectedStatuses.has(status)}
                    onChange={handleStatusChange}
                />
                <span className="ml-2 inline-flex items-center">{iconHtml} {labelText} <span className="count">({getCount('status', status)})</span></span>
            </label>
        );
    });

    const priorityFilters = ['High', 'Medium', 'Low'].map(priority => (
        <label key={priority} className="flex items-center text-gray-700">
            <input
                type="checkbox"
                data-filter-priority={priority}
                className="form-checkbox"
                checked={selectedPriorities.has(priority)}
                onChange={handlePriorityChange}
            />
            <span className="ml-2">{priority} Priority <span className="count">({getCount('priority', priority)})</span></span>
        </label>
    ));

    const categoryTypes = [...new Set(notifications.map(n => n.type))].sort((a, b) => {
        const order = ['order', 'promotion', 'payment', 'support', 'security', 'stock', 'cart', 'review', 'system'];
        const indexA = order.indexOf(a);
        const indexB = order.indexOf(b);
        if (indexA === -1 || indexB === -1) return a.localeCompare(b);
        return indexA - indexB;
    });

    const categoryFilters = categoryTypes.map(type => (
        <label key={type} className="flex items-center text-gray-700">
            <input
                type="checkbox"
                data-filter-category={type}
                className="form-checkbox"
                checked={selectedCategories.has(type)}
                onChange={handleCategoryChange}
            />
            <span className="ml-2">{type.charAt(0).toUpperCase() + type.slice(1)} <span className="count">({getCount('category', type)})</span></span>
        </label>
    ));

    return (
        <aside id="full-page-sidebar-filters" className="w-full lg:w-1/4">
            <div className="bg-white p-6 rounded-lg shadow">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Filters</h2>
                <div className="relative mb-4">
                    <input
                        type="text"
                        id="notification-search-full-page"
                        placeholder="Search notifications..."
                        className="shadow-sm"
                        value={currentSearchQuery}
                        onChange={(e) => setCurrentSearchQuery(e.target.value)}
                    />
                    <button
                        id="clear-search-full-page"
                        className={`absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700 ${currentSearchQuery === '' ? 'hidden' : ''}`}
                        onClick={() => setCurrentSearchQuery('')}
                    >
                        <span className="material-icons-outlined">cancel</span>
                    </button>
                </div>

                <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-700 mb-3">Status</h3>
                    <div className="space-y-2">
                        {statusFilters}
                    </div>
                </div>
                <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-700 mb-3">Priority</h3>
                    <div className="space-y-2">
                        {priorityFilters}
                    </div>
                </div>
                <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-700 mb-3">Category</h3>
                    <div id="full-page-category-filters" className="space-y-2">
                        {categoryFilters}
                    </div>
                </div>
                <button id="clear-filters-full-page" className="filter-btn mt-4 w-full" onClick={onClearAllFilters}>Clear All Filters</button>
            </div>
        </aside>
    );
});

export default FullPageFilters;