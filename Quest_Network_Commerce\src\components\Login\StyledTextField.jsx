import React, { useState } from "react";
import { TextField, InputAdornment, IconButton, Select, MenuItem, Box } from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { parsePhoneNumberFromString, getCountryCallingCode } from "libphonenumber-js";

const StyledTextField = ({
  label,
  value,
  onChange,
  error,
  helperText,
  type = "text",
  showPasswordToggle = false,
  validation = {},
  ...rest
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [localError, setLocalError] = useState("");

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const validateInput = (inputValue) => {
    if (!validation) return "";

    // Email validation
    if (validation.email && inputValue) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(inputValue)) {
        return "Please enter a valid email address";
      }
    }

    // Password validation
    if (validation.password && inputValue) {
      if (inputValue.length < 8) {
        return "Password must be at least 8 characters long";
      }
      if (!/(?=.*[a-z])/.test(inputValue)) {
        return "Password must contain at least one lowercase letter";
      }
      if (!/(?=.*[A-Z])/.test(inputValue)) {
        return "Password must contain at least one uppercase letter";
      }
      if (!/(?=.*\d)/.test(inputValue)) {
        return "Password must contain at least one number";
      }
      if (!/(?=.*[@$!%*?&])/.test(inputValue)) {
        return "Password must contain at least one special character";
      }
    }

    // Phone validation
    if (validation.phone && inputValue) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      if (!phoneRegex.test(inputValue.replace(/\s/g, ''))) {
        return "Please enter a valid phone number";
      }
    }

    // Required validation
    if (validation.required && !inputValue) {
      return `${label} is required`;
    }

    // Min length validation
    if (validation.minLength && inputValue && inputValue.length < validation.minLength) {
      return `${label} must be at least ${validation.minLength} characters long`;
    }

    // Max length validation
    if (validation.maxLength && inputValue && inputValue.length > validation.maxLength) {
      return `${label} must not exceed ${validation.maxLength} characters`;
    }

    return "";
  };

  const handleChange = (e) => {
    const inputValue = e.target.value;
    const validationError = validateInput(inputValue);
    setLocalError(validationError);

    if (onChange) {
      onChange(e);
    }
  };

  const displayError = error || localError;
  const displayHelperText = helperText || localError;

  const inputType = showPasswordToggle ? (showPassword ? "text" : "password") : type;

  return (
    <TextField
      fullWidth
      label={label}
      variant="outlined"
      type={inputType}
      value={value}
      onChange={handleChange}
      error={!!displayError}
      helperText={displayHelperText}
      InputProps={{
        endAdornment: showPasswordToggle ? (
          <InputAdornment position="end">
            <IconButton
              aria-label="toggle password visibility"
              onClick={handleTogglePasswordVisibility}
              edge="end"
              sx={{ color: "#23A3AD" }}
            >
              {showPassword ? <VisibilityOff /> : <Visibility />}
            </IconButton>
          </InputAdornment>
        ) : null,
        sx: {
          borderRadius: "12px",
          backgroundColor: "#f9f9f9",
          fontSize: "0.9rem",
          fontWeight: 500,
          height: "56px",
          fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: "#2EC0CB",
          },
          '&.Mui-focused': {
            backgroundColor: "#ffffff",
            boxShadow: "0 0 0 2px rgba(46, 192, 203, 0.2)",
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: "#2EC0CB",
            borderWidth: "2px",
          },
          '&.Mui-error .MuiOutlinedInput-notchedOutline': {
            borderColor: "#f44336",
          },
          '&.Mui-error:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: "#f44336",
          },
        }
      }}
      InputLabelProps={{
        // Dynamic shrink based on focus or value
        shrink: undefined, // Let Material-UI handle this automatically
        sx: {
          color: "#666",
          fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
          fontSize: "1rem",
          fontWeight: 500,
          transformOrigin: "top left",
          transition: "all 300ms cubic-bezier(0.4, 0, 0.2, 1)",
          // When not focused and no value (placeholder state)
          transform: "translate(14px, 16px) scale(1)",
          // When focused or has value (label state)
          "&.MuiInputLabel-shrink": {
            color: "#2EC0CB",
            transform: "translate(14px, -9px) scale(0.75)",
            fontWeight: 600,
            backgroundColor: "#ffffff",
            padding: "0 8px",
            marginLeft: "-4px",
          },
          "&.Mui-focused": {
            color: "#2EC0CB",
            fontWeight: 600,
          },
          "&.Mui-error": {
            color: "#f44336"
          },
          "&.Mui-error.MuiInputLabel-shrink": {
            color: "#f44336",
            backgroundColor: "#ffffff",
            padding: "0 8px",
            marginLeft: "-4px",
          }
        }
      }}
      FormHelperTextProps={{
        sx: {
          fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
          fontSize: "0.75rem",
        }
      }}
      {...rest}
    />
  );
};

export default StyledTextField;
