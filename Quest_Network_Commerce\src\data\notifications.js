// src/data/notifications.js
const initialNotifications = [
    { id: 1, type: 'order', status: 'unread', archived: false, starred: false, priority: 'High', title: 'Urgent: Your order #12345 has been shipped!', message: 'Your package is estimated to arrive on June 18, 2025. You can track its journey in real-time.', details: 'Your package is estimated to arrive on June 18, 2025. You can track its journey in real-time. Click to view order details and track your delivery.', timestamp: '2025-06-23T16:30:00Z', link: '#order-12345' },
    { id: 2, type: 'promotion', status: 'unread', archived: false, starred: false, priority: 'Medium', title: 'Flash Sale! Get 20% off selected electronics.', message: 'Get 20% off on selected electronics.', details: 'This exclusive offer ends on June 20, 2025. Use code FLASH20 at checkout. Shop now to grab amazing deals!', timestamp: '2025-06-23T15:45:00Z', link: '#promo-electronics' },
    { id: 3, type: 'system', status: 'read', archived: false, starred: false, priority: 'Low', title: 'System Maintenance Scheduled', message: 'Scheduled maintenance on June 17th from 2 AM to 4 AM UTC.', details: 'During this period, some services may be temporarily unavailable. We apologize for any inconvenience. For more information, visit our status page.', timestamp: '2025-06-22T18:00:00Z', link: '#system-maintenance' },
    { id: 4, type: 'order', status: 'unread', archived: false, starred: false, priority: 'Medium', title: 'New Order Received', message: 'Thank you for your order #12346!', details: 'Your order is being prepared for shipment. You will receive a notification with tracking info soon. View your order summary.', timestamp: '2025-06-23T16:05:00Z', link: '#order-12346' },
    { id: 5, type: 'payment', status: 'unread', archived: false, starred: false, priority: 'High', title: 'Payment Processed Successfully', message: 'Payment for invoice INV-2025-001 was successful.', details: 'Amount: $125.00. Payment method: Credit Card. Your updated invoice is available for download.', timestamp: '2025-06-23T15:15:00Z', link: '#payment-inv001' },
    { id: 6, type: 'support', status: 'read', archived: false, starred: true, priority: 'Medium', title: 'Support Ticket Updated', message: 'New response on ticket #7890.', details: 'Our support agent has provided a solution for your issue. Please review the response in your support portal. You can reply directly from the ticket.', timestamp: '2025-06-22T13:05:00Z', link: '#support-7890' },
    { id: 7, type: 'security', status: 'unread', archived: false, starred: false, priority: 'High', title: 'Suspicious Login Attempt Detected', message: 'Login attempt from an unrecognized device.', details: 'Location: Unknown. Device: Windows PC. If this was not you, please change your password immediately. Review your recent activity.', timestamp: '2025-06-23T14:10:00Z', link: '#security-alert' },
    { id: 8, type: 'stock', status: 'read', archived: false, starred: false, priority: 'Low', title: 'Product Back in Stock!', message: 'The "Super Widget Pro" is now available!', details: 'Limited stock available! Purchase now to ensure you don\'t miss out. Add to cart to secure yours!', timestamp: '2025-06-21T15:20:00Z', link: '#stock-widgetpro' },
    { id: 9, type: 'order', status: 'read', archived: false, starred: true, priority: 'Medium', title: 'Your Delivery is Today!', message: 'Order #12340 is out for delivery.', details: 'The driver is estimated to arrive between 2 PM and 5 PM. Please ensure someone is available to receive the package. Track your delivery live.', timestamp: '2025-06-20T08:15:00Z', link: '#order-12340' },
    { id: 10, type: 'system', status: 'read', archived: true, starred: false, priority: 'Low', title: 'App Update Available', message: 'Version 2.0 of our app is now available.', details: 'Download the latest version for new features and performance improvements. Update now for the best experience.', timestamp: '2025-06-14T10:00:00Z', link: '#app-update' },
    { id: 11, type: 'promotion', status: 'read', archived: true, starred: false, priority: 'Low', title: 'Expired Discount Coupon', message: 'Your 10% off coupon has expired.', details: 'Keep an eye out for future exciting offers! Check out other available promotions.', timestamp: '2025-06-13T12:00:00Z', link: '#expired-coupon' },
    { id: 12, type: 'payment', status: 'remind_later', archived: false, starred: false, priority: 'High', title: 'Invoice Due Soon', message: 'Invoice #INV-007 is due in 3 days.', details: 'Please make sure to settle your payment to avoid service interruption. Pay now or view invoice details.', timestamp: '2025-06-23T10:00:00Z', remindAt: '2025-06-24T10:00:00Z', link: '#invoice-007' },
    { id: 13, type: 'support', status: 'unread', archived: false, starred: false, priority: 'Medium', title: 'New Article: Troubleshooting Payments', message: 'Check out our latest help article on common payment issues.', details: 'This article provides step-by-step guidance on resolving various payment-related problems. Read the full article here.', timestamp: '2025-06-23T11:00:00Z', link: '#help-article-payments' },
    { id: 14, type: 'cart', status: 'unread', archived: false, starred: false, priority: 'High', title: 'K&N Air Filter is still in your cart', message: 'Don\'t miss out on your K&N Air Filter! It\'s waiting for you.', details: 'Complete your purchase now to get your K&N Air Filter. Customers who bought this also viewed high-performance spark plugs.', timestamp: '2025-06-24T09:00:00Z', link: '#cart-abandoned' },
    { id: 15, type: 'review', status: 'unread', archived: false, starred: false, priority: 'Medium', title: 'Leave a review for your recent purchase', message: 'Help other shoppers by reviewing your recent purchase: "Premium Car Wax".', details: 'Your feedback is valuable! Click here to share your experience and earn reward points.', timestamp: '2025-06-24T10:00:00Z', link: '#review-purchase-12345' },
];

export default initialNotifications;