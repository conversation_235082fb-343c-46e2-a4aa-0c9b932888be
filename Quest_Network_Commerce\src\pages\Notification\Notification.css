/* src/index.css */

/* BASE & UTILITY STYLES */
:root {
    /* Color Palette */
    --color-software-teal: #2EC0CB;
    --color-software-teal-dark: #23A3AD;
    /* Darker for hover */
    --color-dark-blue: #1D4ED8;
    --color-software-blue: #2563EB;
    --color-dark-teal: #0F766E;
    --color-light-blue: #0EA5E9;
    --color-midnight-blue: #1E293B;
    --color-midnight-blue-dark: #4A4E52;
    /* Darker for hover */
    --color-mid-blue: #3B82F6;
    --color-gold: #FFD700;

    /* Grays (Tailwind-like shades) */
    --color-gray-50: #F9FAFB;
    /* Used as body bg and hover on cards */
    --color-gray-100: #F3F4F6;
    /* Used for archived bg and some hovers */
    --color-gray-200: #E5E7EB;
    /* Used for default btn bg, count badges */
    --color-gray-300: #D1D5DB;
    /* Used for borders */
    --color-gray-400: #9CA3AF;
    /* Used for archived border */
    --color-gray-500: #6B7280;
    /* Used for text, icons, placeholders */
    --color-gray-600: #4B5563;
    --color-gray-700: #374151;
    /* Deeper gray for text/buttons */
    --color-gray-800: #1F2937;
    --color-gray-900: #111827;
    /* Very dark gray for overlay */

    /* Blues (Tailwind-like shades) */
    --color-blue-100: #DBEAFE;
    --color-blue-200: #BFDBFE;
    /* Active filter background */
    --color-blue-600: #2563EB;
    /* Primary blue for checkboxes, links */
    --color-blue-700: #1D4ED8;
    /* Darker blue for hover */
    --color-blue-800: #1E40AF;
    /* Active filter text */

    /* Reds (Tailwind-like shades) */
    --color-red-100: #FEE2E2;
    /* Priority high bg, close toast hover */
    --color-red-300: #FCA5A5;
    /* Unread count badge */
    --color-red-600: #EF4444;
    /* Error text, delete buttons */
    --color-red-700: #DC2626;
    /* Delete hover */
    --color-red-900: #7F1D1D;
    /* Unread count text */

    /* Ambers (Tailwind-like shades) */
    --color-amber-50: #FFFBEB;
    /* Remind later bg */
    --color-amber-100: #FEF3C7;
    /* Remind later hover */
    --color-amber-500: #F59E0B;
    /* Remind later hover icon color */
    --color-amber-600: #D97706;
    /* Remind later border, text */
    --color-amber-700: #B45309;
    /* Remind later text */

    /* Sky (Tailwind-like shades) */
    --color-sky-50: #F0F9FF;
    /* Unread card bg */
    --color-sky-100: #E0F2FE;
    /* Unread card hover */

    /* Greens (Tailwind-like shades) */
    --color-green-100: #D1FAE5;
    /* Priority low bg */
    --color-green-500: #10B981;
    /* Success button */
    --color-green-600: #059669;
    /* Success button hover, priority low text */
    --color-green-700: #047857;
    /* Toast success text */

    /* Default Border */
    --color-default-border: #E2E8F0;
    /* Default border color for elements */
}

/* Base styles and resets */
*,
*::before,
*::after {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--color-gray-50);
    margin: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    line-height: 1.5;
    color: var(--color-gray-700);
    font-size: 16px;
}

/* Custom Scrollbar Styles */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--color-gray-100);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--color-gray-400);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--color-gray-500);
}

/* Utility Classes (mimicking Tailwind where necessary for layout/common styles) */
.bg-none {
    background: none;
}

.border-none {
    border: none;
}

.flex {
    display: flex;
}

.inline-flex {
    display: inline-flex;
}

.flex-col {
    flex-direction: column;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-none {
    flex: none;
}

.flex-grow {
    flex-grow: 1;
}

.flex-1 {
    flex: 1;
}

.items-center {
    align-items: center;
}

.items-start {
    align-items: flex-start;
}

.justify-between {
    justify-content: space-between;
}

.justify-end {
    justify-content: flex-end;
}

.justify-center {
    justify-content: center;
}

.mr-auto {
    margin-right: auto;
}

.gap-1 {
    gap: 0.25rem;
}

.gap-2 {
    gap: 0.5rem;
}

.gap-4 {
    gap: 1rem;
}

.gap-8 {
    gap: 2rem;
}

.space-x-2 > *:not(:first-child) {
    margin-left: 0.5rem;
}

.space-x-4 > *:not(:first-child) {
    margin-left: 1rem;
}

.space-y-2 > *:not(:first-child) {
    margin-top: 0.5rem;
}

.space-y-4 > *:not(:first-child) {
    margin-top: 1rem;
}

.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

.inset-0 {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.top-0 {
    top: 0;
}

.right-0 {
    right: 0;
}

.bottom-0 {
    bottom: 0;
}

.left-0 {
    left: 0;
}

.pr-2 {
    padding-right: 0.5rem;
}

.pr-3 {
    padding-right: 0.75rem;
}

.pl-2 {
    padding-left: 0.5rem;
}

.pl-7 {
    padding-left: 1.75rem;
}

.pb-2 {
    padding-bottom: 0.5rem;
}

.py-0\.5 {
    padding-top: 0.125rem;
    padding-bottom: 0.125rem;
}

.py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
}

.py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
}

.px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

.px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

.p-2 {
    padding: 0.5rem;
}

.p-4 {
    padding: 1rem;
}

.p-6 {
    padding: 1.5rem;
}

.p-8 {
    padding: 2rem;
}

.w-full {
    width: 100%;
}

.w-1\/4 {
    width: 25%;
}

.w-5 {
    width: 1.25rem;
}

.w-8 {
    width: 2rem;
}

.h-4 {
    height: 1rem;
}

.min-w-\[200px\] {
    min-width: 200px;
}

.max-w-xs {
    max-width: 20rem;
}

.max-w-sm {
    max-width: 24rem;
}

.max-w-md {
    max-width: 28rem;
}

.max-w-lg {
    max-width: 32rem;
}

.max-w-xl {
    max-width: 36rem;
}

.max-w-4xl {
    max-width: 56rem;
}

.max-w-7xl {
    max-width: 80rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.mb-1 {
    margin-bottom: 0.25rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 0.75rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.mt-1 {
    margin-top: 0.25rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-4 {
    margin-top: 1rem;
}

.mt-8 {
    margin-top: 2rem;
}

.mt-16 {
    margin-top: 4rem;
}

.mt-32 {
    margin-top: 8rem;
}

.ml-2 {
    margin-left: 0.5rem;
}

.ml-4 {
    margin-left: 1rem;
}

.mr-1 {
    margin-right: 0.25rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.mr-3 {
    margin-right: 0.75rem;
}

.hidden {
    display: none !important;
}

.block {
    display: block;
}

.text-center {
    text-align: center;
}

.text-sm {
    font-size: 0.875rem;
}

.text-xs {
    font-size: 0.75rem;
}

.text-base {
    font-size: 1rem;
}

.text-lg {
    font-size: 1.125rem;
}

.text-xl {
    font-size: 1.25rem;
}

.text-2xl {
    font-size: 1.5rem;
}

.text-3xl {
    font-size: 1.875rem;
}

.text-4xl {
    font-size: 2.25rem;
}

.font-light {
    font-weight: 300;
}

.font-normal {
    font-weight: 400;
}

.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

.font-bold {
    font-weight: 700;
}

.rounded-md {
    border-radius: 0.375rem;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.rounded-full {
    border-radius: 9999px;
}

.rounded-sm {
    border-radius: 0.125rem;
}

.shadow {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.border {
    border-width: 1px;
    border-style: solid;
    border-color: var(--color-gray-300);
}

.border-left-4 {
    border-left-width: 4px;
    border-left-style: solid;
}

.border-bottom-1 {
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-color: var(--color-gray-200);
    padding-bottom: 0.75rem;
}


.transition-opacity {
    transition: opacity 0.3s ease-out;
}

.transition-all {
    transition: all 0.3s ease-out;
}

.duration-300 {
    transition-duration: 0.3s;
}

.duration-150 {
    transition-duration: 0.15s;
}

.scale-95 {
    transform: scale(0.95);
}

.opacity-0 {
    opacity: 0;
}

.whitespace-nowrap {
    white-space: nowrap;
}

.pointer-events-none {
    pointer-events: none;
}

.pointer-events-auto {
    pointer-events: auto;
}

.overflow-y-auto {
    overflow-y: auto;
}

.-webkit-overflow-scrolling-touch {
    -webkit-overflow-scrolling: touch;
}

.z-10 {
    z-index: 10;
}

.z-20 {
    z-index: 20;
}

.z-50 {
    z-index: 50;
}

.z-100 {
    z-index: 100;
}

.min-h-screen {
    min-height: 100vh;
}

.cursor-pointer {
    cursor: pointer;
}

.outline-none:focus {
    outline: none;
}

/* Form Elements - input, checkbox */
input[type="text"] {
    border: 1px solid var(--color-gray-300);
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    font-size: 0.875rem;
    line-height: 1.25;
    width: 100%;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

input[type="text"]:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    border-color: var(--color-blue-600);
    box-shadow: 0 0 0 1px var(--color-blue-600);
}

.form-checkbox {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    height: 1rem;
    width: 1rem;
    border: 1px solid var(--color-gray-400);
    border-radius: 0.125rem;
    background-color: #fff;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
    flex-shrink: 0;
}

.form-checkbox:checked {
    background-color: var(--color-blue-600);
    border-color: var(--color-blue-600);
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 00-1.414 0L7 8.586 4.207 5.793a1 1 0 00-1.414 1.414l3.5 3.5a1 1 0 001.414 0l5-5a1 1 0 000-1.414z'/%3e%3c/svg%3e");
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
}

.form-checkbox:indeterminate {
    background-color: var(--color-blue-600);
    border-color: var(--color-blue-600);
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 24 24' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M4 11h16v2H4z'/%3e%3c/svg%3e");
    background-size: 70% 70%;
    background-position: center;
    background-repeat: no-repeat;
}

/* Page Layouts & Visibility */
#notification-panel {
    display: flex;
    flex-direction: column;
    width: 100%;
    min-height: 100vh;
}

#full-notifications-page,
#notification-detail-page {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-gray-50);
    z-index: 40;
    overflow-y: auto;
    animation: fadeIn 0.3s ease-out forwards;
}

#full-notifications-page.active,
#notification-detail-page.active {
    display: block;
}

/* Notification List Container Enhancements */
.notification-list-height {
    flex-grow: 1;
    overflow-y: auto;
    padding-right: 1.5rem;
    padding-bottom: 1.5rem;
    -webkit-overflow-scrolling: touch;
    background-color: var(--color-gray-50);
    padding-left: 1.5rem;
    padding-top: 0.5rem;
}

/* Notification Card Styles (Compact View) */
.notification-card {
    border-left-width: 4px;
    border-left-style: solid;
    margin-bottom: 0.75rem;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: white;
    box-shadow: var(--shadow-sm);
    transition: background-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out, transform 0.2s ease-in-out;
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    position: relative;
    flex-wrap: wrap;
    padding-bottom: 3.5rem;
}

.notification-card > input[type="checkbox"] {
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.notification-card .notification-content {
    flex-grow: 1;
    flex-basis: calc(100% - 1rem - 0.75rem);
}

.notification-card .notification-content .notification-main-info {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
    flex-wrap: wrap;
}

.notification-card .notification-content .material-icons-outlined {
    vertical-align: middle;
    margin-right: 0.5rem;
    font-size: 1.25rem;
}

.notification-card .notification-content .notification-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--color-gray-900);
    line-height: 1.3;
    flex-grow: 1;
}

.notification-card .notification-content .notification-timestamp {
    font-size: 0.75rem;
    color: var(--color-gray-500);
    white-space: nowrap;
    flex-shrink: 0;
    margin-left: auto;
    padding-left: 0.5rem;
}

.notification-card .notification-content .notification-message {
    font-size: 0.875rem;
    color: var(--color-gray-600);
    margin-top: 0.1rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.notification-card.unread {
    background-color: var(--color-sky-50);
    border-left-color: var(--color-software-teal);
    font-weight: 500;
}

.notification-card:hover {
    background-color: var(--color-gray-100);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.notification-card.unread:hover {
    background-color: var(--color-sky-100);
}

.notification-card.archived {
    opacity: 0.7;
    background-color: var(--color-gray-100);
    border-left-color: var(--color-gray-400);
    font-style: italic;
}

.notification-card.archived:hover {
    background-color: var(--color-gray-200);
    opacity: 0.8;
}

.notification-card.remind-later {
    background-color: var(--color-amber-50);
    border-left-color: var(--color-amber-600);
}

.notification-card.remind-later:hover {
    background-color: var(--color-amber-100);
}

.notification-card.starred {
    border-right: 4px solid var(--color-gold);
}

/* Per-notification action buttons (Compact View) */
.notification-card-actions {
    position: absolute;
    bottom: 0.75rem;
    right: 1rem;
    display: flex;
    gap: 0.25rem;
    opacity: 1;
    pointer-events: auto;
    transition: none;
    z-index: 5;
}

.notification-card-actions button {
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--color-gray-200);
    border-radius: 0.25rem;
    padding: 0.3rem 0.5rem;
    font-size: 0.75rem;
    color: var(--color-gray-700);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.15s ease-in-out;
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    backdrop-filter: blur(2px);
}

.notification-card-actions button .material-icons-outlined {
    font-size: 0.85rem;
    margin-right: 0.25rem;
}

.notification-card-actions button:hover {
    background-color: var(--color-gray-100);
    color: var(--color-gray-800);
    border-color: var(--color-gray-300);
}

.notification-card-actions button.mark-read-btn:hover {
    color: var(--color-software-teal);
}

.notification-card-actions button.mark-unread-btn:hover {
    color: var(--color-software-teal);
}

.notification-card-actions button.archive-btn:hover {
    color: var(--color-amber-600);
}

.notification-card-actions button.remind-later-btn:hover {
    color: var(--color-amber-500);
}

.notification-card-actions button.delete-btn:hover {
    color: var(--color-red-600);
}

.notification-card-actions button.star-btn {
    color: var(--color-gold);
}

.notification-card-actions button.star-btn.active {
    color: var(--color-gold);
}

/* Border colors for notification types */
.border-order {
    border-left-color: var(--color-software-teal);
}

.border-promotion {
    border-left-color: var(--color-dark-blue);
}

.border-system {
    border-left-color: var(--color-software-blue);
}

.border-payment {
    border-left-color: var(--color-dark-teal);
}

.border-support {
    border-left-color: var(--color-light-blue);
}

.border-security {
    border-left-color: var(--color-midnight-blue);
}

.border-stock {
    border-left-color: var(--color-mid-blue);
}

.border-cart {
    border-left-color: var(--color-red-600);
}

.border-review {
    border-left-color: var(--color-green-600);
}

/* Full-screen notification detail view styles */
#notification-detail-view {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-lg);
    padding: 2.5rem;
    margin-top: 1.5rem;
    border: 1px solid var(--color-gray-200);
}

#notification-detail-view .detail-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--color-gray-200);
}

#notification-detail-view .detail-header .material-icons-outlined {
    font-size: 3rem;
    margin-right: 1.25rem;
    line-height: 1;
    flex-shrink: 0;
}

#notification-detail-view .detail-content-wrap {
    flex-grow: 1;
}

#notification-detail-view .detail-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--color-gray-900);
    line-height: 1.2;
    margin-bottom: 0.5rem;
}

#notification-detail-view .detail-timestamp {
    font-size: 0.875rem;
    color: var(--color-gray-500);
    margin-left: auto;
    white-space: nowrap;
    flex-shrink: 0;
    padding-top: 0.5rem;
}

#notification-detail-view .detail-message {
    font-size: 1.125rem;
    color: var(--color-gray-700);
    margin-bottom: 1.25rem;
    line-height: 1.6;
    text-align: justify;
}

#notification-detail-view .detail-details {
    font-size: 1rem;
    color: var(--color-gray-600);
    margin-bottom: 2rem;
    line-height: 1.7;
    background-color: var(--color-gray-50);
    border-left: 3px solid var(--color-blue-100);
    padding: 1rem 1.25rem;
    border-radius: 0.25rem;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

#notification-detail-view .detail-details .material-icons-outlined {
    font-size: 1.5rem;
    color: var(--color-blue-600);
    flex-shrink: 0;
}


#notification-detail-view .detail-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    border-top: 1px solid var(--color-gray-100);
    padding-top: 1.5rem;
    margin-top: 1rem;
}

#notification-detail-page .header-banner {
    background-color: var(--color-software-teal);
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-md);
    position: sticky;
    top: 0;
    z-index: 10;
}

#notification-detail-page .header-banner #back-to-list-from-detail-btn {
    background: none;
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.9rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
    margin-right: 1rem;
}

#notification-detail-page .header-banner #back-to-list-from-detail-btn .material-icons-outlined {
    font-size: 1.3rem;
    margin-right: 0.5rem;
}

#notification-detail-page .header-banner #back-to-list-from-detail-btn:hover {
    background-color: var(--color-software-teal-dark);
}

#notification-detail-page .header-banner #detail-view-notification-type {
    font-size: 1.5rem;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}


.rounded-lg-custom {
    border-radius: 0.5rem;
}

.category-group {
    margin-bottom: 2rem;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--color-gray-200);
}

.category-header-plain {
    padding: 1rem 1.5rem;
    font-weight: 600;
    color: var(--color-gray-700);
    border-bottom: 1px solid var(--color-default-border);
    border-radius: 0.5rem 0.5rem 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.full-detail-card {
    border-left-width: 4px;
    border-left-style: solid;
    padding: 1.25rem 1.5rem;
    background-color: white;
    cursor: pointer;
    border-bottom: 1px solid var(--color-gray-100);
    display: flex;
    align-items: flex-start;
    gap: 1.25rem;
    transition: background-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out, transform 0.2s ease-in-out;
}

.full-detail-card:last-child {
    border-bottom: none;
    border-radius: 0 0 0.5rem 0.5rem;
}

.full-detail-card.read {
    background-color: white;
}

.full-detail-card.unread {
    background-color: var(--color-sky-50);
    border-left-color: var(--color-software-teal);
}

.full-detail-card.remind-later {
    background-color: var(--color-amber-50);
    border-left-color: var(--color-amber-600);
}

.full-detail-card.starred {
    border-right: 4px solid var(--color-gold);
}

.full-detail-card:hover {
    background-color: var(--color-gray-50);
    box-shadow: var(--shadow-sm);
    transform: translateY(-2px);
}

.notification-count-summary {
    font-size: 0.875rem;
    color: var(--color-gray-500);
    margin-left: 1rem;
    font-weight: normal;
}

.notification-count-summary .unread-count {
    color: var(--color-red-600);
    font-weight: 600;
}

/* Full detail card action buttons */
.full-detail-action-btn {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 0.25rem;
    padding: 0.35rem 0.6rem;
    font-size: 0.75rem;
    color: var(--color-gray-600);
    transition: all 0.15s ease-in-out;
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
}

.full-detail-action-btn .material-icons-outlined {
    font-size: 1.1rem;
    margin-right: 0.25rem;
}

.full-detail-action-btn:hover {
    background-color: var(--color-gray-100);
    border-color: var(--color-gray-200);
    color: var(--color-gray-700);
}

.full-detail-action-btn.mark-read-btn:hover {
    color: var(--color-software-teal);
}

.full-detail-action-btn.mark-unread-btn:hover {
    color: var(--color-software-teal);
}

.full-detail-action-btn.archive-btn:hover {
    color: var(--color-amber-600);
}

.full-detail-action-btn.remind-later-btn:hover {
    color: var(--color-amber-500);
}

.full-detail-action-btn.delete-btn:hover {
    color: var(--color-red-600);
}

.full-detail-action-btn.star-btn {
    color: var(--color-gold);
}

.full-detail-action-btn.star-btn.active {
    color: var(--color-gold);
}


.filter-btn {
    background-color: white;
    border: 1px solid var(--color-gray-300);
    color: var(--color-gray-700);
    transition: all 0.2s ease-in-out;
    padding: 0.35rem 0.85rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

.filter-btn:hover {
    background-color: var(--color-gray-100);
    border-color: var(--color-gray-400);
}

.filter-btn.active-filter {
    background-color: var(--color-blue-200);
    border-color: var(--color-blue-600);
    color: var(--color-blue-800);
    font-weight: 500;
    box-shadow: 0 0 0 1px var(--color-blue-600);
}

.filter-btn .count {
    font-size: 0.7rem;
    margin-left: 0.4rem;
    padding: 0.15rem 0.5rem;
    border-radius: 9999px;
    background-color: var(--color-gray-200);
    color: var(--color-gray-700);
}

.filter-btn.active-filter .count {
    background-color: var(--color-blue-100);
    color: var(--color-blue-800);
}

.filter-btn.active-filter.unread-filter .count {
    background-color: var(--color-red-300);
    color: var(--color-red-900);
}

.filter-btn .material-icons-outlined {
    font-size: 1rem;
    margin-right: 0.25rem;
}


/* Custom button colors */
.btn-primary,
.btn-secondary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    padding: 0.6rem 1.25rem;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    box-shadow: var(--shadow-sm);
    border: 1px solid transparent;
}

.btn-primary:hover,
.btn-secondary:hover {
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background-color: var(--color-software-teal);
    color: white;
}

.btn-primary:hover {
    background-color: var(--color-software-teal-dark);
}

.btn-secondary {
    background-color: var(--color-gray-700);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--color-gray-800);
}

/* Specifically for action buttons within the detail view to match general buttons */
#notification-detail-view .detail-actions .btn-primary,
#notification-detail-view .detail-actions .btn-secondary {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 400;
    border-radius: 0.375rem;
}

#notification-detail-view .detail-actions .btn-secondary.delete {
    background-color: var(--color-red-600);
    color: white;
}

#notification-detail-view .detail-actions .btn-secondary.delete:hover {
    background-color: var(--color-red-700);
}

#notification-detail-view .detail-actions .btn-secondary.active {
    background-color: var(--color-amber-50);
    color: var(--color-gold);
    border-color: var(--color-amber-300);
}

#notification-detail-view .detail-actions .btn-secondary.active:hover {
    background-color: var(--color-amber-100);
    border-color: var(--color-amber-500);
}

.btn-selected-action {
    background-color: var(--color-blue-600);
    color: white;
    border: none;
    padding: 0.6rem 1.25rem;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    box-shadow: var(--shadow-sm);
}

.btn-selected-action:hover {
    background-color: var(--color-blue-700);
    box-shadow: var(--shadow-md);
}

.btn-selected-action.archive {
    background-color: var(--color-amber-500);
}

.btn-selected-action.archive:hover {
    background-color: var(--color-amber-600);
}

.btn-selected-action.delete {
    background-color: var(--color-red-600);
}

.btn-selected-action.delete:hover {
    background-color: var(--color-red-700);
}


/* Full page header background */
#full-notifications-page > div > .header-banner {
    background-color: var(--color-software-teal);
}

#back-to-dashboard-btn {
    background: none;
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
}

#back-to-dashboard-btn .material-icons-outlined {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

#back-to-dashboard-btn:hover {
    background-color: var(--color-software-teal-dark);
}

/* Confirmation Modal Styles */
#custom-confirm-modal {
    background-color: rgba(17, 24, 37, 0.6);
    opacity: 0;
    transition: opacity 0.3s ease-out;
}

#custom-confirm-modal.active {
    opacity: 1;
}

#custom-confirm-box {
    transform: scale(0.95);
    transition: all 0.2s ease-out;
    background: white;
}

#custom-confirm-modal.active #custom-confirm-box {
    transform: scale(1);
}

#custom-confirm-ok-btn {
    background-color: var(--color-midnight-blue);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
}

#custom-confirm-ok-btn:hover {
    background-color: var(--color-midnight-blue-dark);
}

#custom-confirm-cancel-btn {
    background-color: var(--color-gray-200);
    color: var(--color-gray-700);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
}

#custom-confirm-cancel-btn:hover {
    background-color: var(--color-gray-300);
}


/* Direct notification (toast) container remains fixed */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 100;
    width: 350px;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
    pointer-events: none;
}

.toast-message {
    background-color: white;
    border-left: 4px solid;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    transform: translateX(100%);
    opacity: 0;
    /* Changed animation to use classes */
    pointer-events: auto;
    position: relative;
    font-size: 0.95rem;
    line-height: 1.25;
}

.toast-message.slide-in {
    transform: translateX(0);
    opacity: 1;
    transition: transform 0.5s ease-out, opacity 0.5s ease-out;
}

.toast-message.slide-out {
    transform: translateX(100%);
    opacity: 0;
    transition: transform 0.5s ease-out, opacity 0.5s ease-out;
}

.toast-message .material-icons-outlined {
    margin-right: 0.5rem;
    font-size: 1.2rem;
    line-height: 1;
}

.toast-message .close-toast {
    position: absolute;
    top: 8px;
    right: 8px;
    background: none;
    border: none;
    font-size: 1rem;
    color: var(--color-gray-500);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s, background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toast-message .close-toast .material-icons-outlined {
    font-size: 1.1rem;
    margin: 0;
}

.toast-message .close-toast:hover {
    color: var(--color-red-600);
    background-color: var(--color-red-100);
}

.toast-message.success {
    border-color: var(--color-software-teal);
    color: var(--color-green-700);
}

.toast-message.info {
    border-color: var(--color-software-blue);
    color: var(--color-dark-blue);
}

.toast-message.error {
    border-color: var(--color-red-600);
    color: var(--color-red-700);
}

/* Keyframe animations */
@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Header & Sticky Elements */
#notification-panel .header-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
}

#notification-panel .header-actions-group {
    display: inline-grid;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.header-select-all {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: var(--color-gray-700);
    font-weight: 500;
}

.sticky-header {
    position: sticky;
    top: 0;
    z-index: 20;
    background-color: var(--color-gray-50);
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--color-default-border);
    margin-bottom: 1rem;
}

#notification-panel .flex-none {
    background-color: var(--color-gray-50);
}

#full-notifications-page > div > .header-banner {
    background-color: var(--color-software-teal);
    position: sticky;
    top: 0;
    z-index: 20;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

#full-notifications-page .full-page-actions-bar {
    position: sticky;
    top: 6rem;
    z-index: 19;
    background-color: var(--color-gray-50);
    padding-top: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--color-default-border);
    margin-bottom: 1rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 0.5rem;
}

#full-notifications-page .full-page-actions-bar .header-select-all {
    margin-right: auto;
}


/* Button Loading States */
.button-loading {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
}

.button-loading.button-success {
    background-color: var(--color-green-500);
}

.button-loading.button-success:hover {
    background-color: var(--color-green-600);
}

.button-loading.button-error {
    background-color: var(--color-red-600);
}

.button-loading.button-error:hover {
    background-color: var(--color-red-700);
}


/* Priority Badges */
.priority-badge {
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.15rem 0.5rem;
    border-radius: 9999px;
    margin-left: 0.5rem;
    white-space: nowrap;
    text-transform: uppercase;
}

.priority-high {
    background-color: var(--color-red-100);
    color: var(--color-red-600);
}

.priority-medium {
    background-color: var(--color-amber-50);
    color: var(--color-amber-600);
}

.priority-low {
    background-color: var(--color-green-100);
    color: var(--color-green-600);
}

/* Responsive adjustments */
@media (min-width: 1024px) {

    .lg\:flex-row {
        flex-direction: row;
    }

    .lg\:w-1\/4 {
        width: 25%;
    }

    .lg\:w-3\/4 {
        width: 75%;
    }

    .lg\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* Search Input Alignment */
.compact-filter-buttons-container .relative {
    display: flex;
    align-items: center;
    flex-grow: 1;
    max-width: 20rem;
}

.compact-filter-buttons-container input[type="text"] {
    flex-grow: 1;
    padding-right: 2.5rem;
}

.compact-filter-buttons-container #clear-search-compact {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: var(--color-gray-500);
    transition: color 0.2s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.25rem;
    height: auto;
}

.compact-filter-buttons-container #clear-search-compact:hover {
    color: var(--color-gray-700);
}

.compact-filter-buttons-container #clear-search-compact .material-icons-outlined {
    font-size: 1.25rem;
}