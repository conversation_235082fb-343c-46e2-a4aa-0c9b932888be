import { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  IconButton,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Chip,
  Autocomplete,
} from "@mui/material";
import { Close, AttachFile, Clear } from "@mui/icons-material";

const ContactUsModal = ({ open, onClose }) => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    countryCode: "+1",
    subject: [],
    description: "",
    attachments: []
  });

  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const subjectOptions = [
    "Login Issues",
    "Password Reset Problems",
    "Account Access Denied",
    "Two-Factor Authentication Issues",
    "Email Verification Problems",
    "Account Locked/Suspended",
    "Username/Email Not Recognized",
    "Social Media Login Issues",
    "Browser Compatibility Issues",
    "Mobile App Login Problems",
    "Other"
  ];

  const countryCodes = [
    { code: "+1", country: "United States", name: "US" },
    { code: "+1", country: "Canada", name: "CA" },
    { code: "+44", country: "United Kingdom", name: "GB" },
    { code: "+91", country: "India", name: "IN" },
    { code: "+86", country: "China", name: "CN" },
    { code: "+49", country: "Germany", name: "DE" },
    { code: "+33", country: "France", name: "FR" },
    { code: "+81", country: "Japan", name: "JP" },
    { code: "+61", country: "Australia", name: "AU" },
    { code: "+55", country: "Brazil", name: "BR" },
    { code: "+7", country: "Russia", name: "RU" },
    { code: "+39", country: "Italy", name: "IT" },
    { code: "+34", country: "Spain", name: "ES" },
    { code: "+82", country: "South Korea", name: "KR" },
    { code: "+52", country: "Mexico", name: "MX" },
    { code: "+31", country: "Netherlands", name: "NL" },
    { code: "+46", country: "Sweden", name: "SE" },
    { code: "+47", country: "Norway", name: "NO" },
    { code: "+41", country: "Switzerland", name: "CH" },
    { code: "+65", country: "Singapore", name: "SG" },
    { code: "+971", country: "UAE", name: "AE" },
    { code: "+966", country: "Saudi Arabia", name: "SA" },
    { code: "+27", country: "South Africa", name: "ZA" },
    { code: "+20", country: "Egypt", name: "EG" },
    { code: "+234", country: "Nigeria", name: "NG" }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubjectChange = (event) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      subject: typeof value === 'string' ? value.split(',') : value
    }));
  };

  const handleSubjectDelete = (subjectToDelete) => {
    setFormData(prev => ({
      ...prev,
      subject: prev.subject.filter(s => s !== subjectToDelete)
    }));
  };

  const clearAllSubjects = () => {
    setFormData(prev => ({
      ...prev,
      subject: []
    }));
  };

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...files]
    }));
  };

  const removeAttachment = (indexToRemove) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, index) => index !== indexToRemove)
    }));
  };

  const isOtherSelected = formData.subject.includes("Other");
  const isDescriptionRequired = isOtherSelected || formData.subject.length > 0;

  const handleSubmit = () => {
    // Validate required fields
    if (!formData.name || !formData.email || formData.subject.length === 0) {
      alert("Please fill in all required fields (Name, Email, Subject)");
      return;
    }

    if (isOtherSelected && !formData.description.trim()) {
      alert("Description is required when 'Other' is selected");
      return;
    }

    console.log("Contact Us Data:", formData);
    setShowSuccessMessage(true);
    
    // Hide success message and close modal after 2 seconds
    setTimeout(() => {
      setShowSuccessMessage(false);
      onClose();
      // Reset form
      setFormData({
        name: "",
        email: "",
        phone: "",
        countryCode: "+1",
        subject: [],
        description: "",
        attachments: []
      });
    }, 2000);
  };

  if (showSuccessMessage) {
    return (
      <Dialog 
        open={open} 
        onClose={() => {}} 
        maxWidth="sm" 
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: "16px",
            textAlign: "center",
            p: 4
          }
        }}
      >
        <DialogContent>
          <Typography variant="h5" sx={{ 
            color: "#2EC0CB", 
            fontWeight: 600, 
            mb: 2,
            fontFamily: "'HCLTechRoobert', sans-serif"
          }}>
            ✅ Ticket Raised Successfully!
          </Typography>
          <Typography variant="body1" sx={{ color: "#666" }}>
            We've received your request and will get back to you soon.
          </Typography>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: "20px",
          maxHeight: "95vh",
          background: "linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)",
          overflow: "hidden"
        }
      }}
    >
      <DialogTitle sx={{
        background: "linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)",
        color: "white",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "flex-start",
        padding: "24px 32px",
        borderBottom: "none"
      }}>
        <Box>
          <Typography variant="h5" sx={{ fontWeight: 700, mb: 0.5 }}>
            Contact Support
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.9, fontWeight: 400 }}>
            We're here to help you with any login or account issues
          </Typography>
        </Box>
        <IconButton
          onClick={onClose}
          sx={{
            color: "white",
            backgroundColor: "rgba(255,255,255,0.1)",
            "&:hover": {
              backgroundColor: "rgba(255,255,255,0.2)"
            }
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: "32px", pt: "24px", backgroundColor: "transparent" }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 2 }}>
          {/* Name Field */}
          <TextField
            label="Full Name (Optional)"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            fullWidth
            placeholder="Enter your full name"
            variant="outlined"
            sx={{
              borderRadius: '12px',
              backgroundColor: '#ffffff',
              '& .MuiOutlinedInput-root': {
                borderRadius: '12px',
                backgroundColor: '#ffffff',
                '&.Mui-focused fieldset': {
                  borderColor: '#2EC0CB',
                  borderWidth: '2px'
                },
                '&:hover fieldset': {
                  borderColor: '#2EC0CB'
                }
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: '#2EC0CB',
                fontWeight: 600
              },
            }}
          />

          {/* Email Field */}
          <TextField
            label="Email Address *"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            fullWidth
            required
            placeholder="Enter your email address"
            variant="outlined"
            sx={{
              borderRadius: '12px',
              backgroundColor: '#ffffff',
              '& .MuiOutlinedInput-root': {
                borderRadius: '12px',
                backgroundColor: '#ffffff',
                '&.Mui-focused fieldset': {
                  borderColor: '#2EC0CB',
                  borderWidth: '2px'
                },
                '&:hover fieldset': {
                  borderColor: '#2EC0CB'
                }
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: '#2EC0CB',
                fontWeight: 600
              },
            }}
          />

          {/* Phone Number with Country Code */}
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Autocomplete
              options={countryCodes}
              getOptionLabel={(option) => `${option.code} (${option.country})`}
              value={countryCodes.find(c => c.code === formData.countryCode) || countryCodes[0]}
              onChange={(_, newValue) => {
                if (newValue) {
                  handleInputChange('countryCode', newValue.code);
                }
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Country"
                  sx={{
                    minWidth: 180,
                    borderRadius: '12px',
                    backgroundColor: '#ffffff',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '12px',
                      backgroundColor: '#ffffff',
                      '&.Mui-focused fieldset': {
                        borderColor: '#2EC0CB',
                        borderWidth: '2px'
                      },
                      '&:hover fieldset': {
                        borderColor: '#2EC0CB'
                      }
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#2EC0CB',
                      fontWeight: 600
                    },
                  }}
                />
              )}
            />
            <TextField
              label="Phone Number *"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              fullWidth
              required
              placeholder="Enter your phone number"
              variant="outlined"
              sx={{
                borderRadius: '12px',
                backgroundColor: '#ffffff',
                '& .MuiOutlinedInput-root': {
                  borderRadius: '12px',
                  backgroundColor: '#ffffff',
                  '&.Mui-focused fieldset': {
                    borderColor: '#2EC0CB',
                    borderWidth: '2px'
                  },
                  '&:hover fieldset': {
                    borderColor: '#2EC0CB'
                  }
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: '#2EC0CB',
                  fontWeight: 600
                },
              }}
            />
          </Box>

          {/* Subject Multi-Select with Clear Button */}
          <FormControl fullWidth required>
            <InputLabel>Subject *</InputLabel>
            <Select
              multiple
              value={formData.subject}
              onChange={handleSubjectChange}
              label="Subject *"
              endAdornment={
                formData.subject.length > 0 && (
                  <IconButton
                    onClick={clearAllSubjects}
                    sx={{ 
                      mr: 1,
                      color: '#2EC0CB',
                      '&:hover': {
                        backgroundColor: '#E0F7F9'
                      }
                    }}
                  >
                    <Clear />
                  </IconButton>
                )
              }
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => (
                    <Chip 
                      key={value} 
                      label={value} 
                      size="small"
                      onDelete={() => handleSubjectDelete(value)}
                      sx={{ 
                        backgroundColor: '#E0F7F9',
                        color: '#2EC0CB',
                        '& .MuiChip-deleteIcon': {
                          color: '#2EC0CB'
                        }
                      }}
                    />
                  ))}
                </Box>
              )}
              sx={{
                borderRadius: '12px',
                backgroundColor: '#ffffff',
                '& .MuiOutlinedInput-notchedOutline': {
                  borderRadius: '12px'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#2EC0CB',
                  borderWidth: '2px'
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#2EC0CB'
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: '#2EC0CB',
                  fontWeight: 600
                }
              }}
            >
              {subjectOptions.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Description */}
          <TextField
            label={isOtherSelected ? "Description *" : "Description (Optional)"}
            multiline
            rows={4}
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            fullWidth
            required={isOtherSelected}
            placeholder="Please describe your issue in detail..."
            variant="outlined"
            sx={{
              borderRadius: '12px',
              backgroundColor: '#ffffff',
              '& .MuiOutlinedInput-root': {
                borderRadius: '12px',
                backgroundColor: '#ffffff',
                '&.Mui-focused fieldset': {
                  borderColor: '#2EC0CB',
                  borderWidth: '2px'
                },
                '&:hover fieldset': {
                  borderColor: '#2EC0CB'
                }
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: '#2EC0CB',
                fontWeight: 600
              },
            }}
          />

          {/* File Upload */}
          <Box>
            <input
              accept="*/*"
              style={{ display: 'none' }}
              id="file-upload"
              multiple
              type="file"
              onChange={handleFileUpload}
            />
            <label htmlFor="file-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={<AttachFile />}
                sx={{
                  borderRadius: '12px',
                  borderColor: '#2EC0CB',
                  color: '#2EC0CB',
                  backgroundColor: '#ffffff',
                  fontWeight: 600,
                  padding: '12px 24px',
                  '&:hover': {
                    borderColor: '#23A3AD',
                    backgroundColor: '#E0F7F9',
                    borderWidth: '2px'
                  }
                }}
              >
                Attach Files (Optional)
              </Button>
            </label>
            {formData.attachments.length > 0 && (
              <Box sx={{ mt: 1 }}>
                {formData.attachments.map((file, index) => (
                  <Chip
                    key={index}
                    label={file.name}
                    onDelete={() => removeAttachment(index)}
                    sx={{ mr: 1, mb: 1 }}
                  />
                ))}
              </Box>
            )}
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{
        p: "32px",
        pt: "24px",
        backgroundColor: "transparent",
        justifyContent: "space-between",
        gap: 2
      }}>
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{
            borderRadius: '12px',
            borderColor: '#E0E0E0',
            color: '#666',
            fontWeight: 600,
            padding: '12px 32px',
            backgroundColor: '#ffffff',
            '&:hover': {
              backgroundColor: '#f5f5f5',
              borderColor: '#2EC0CB'
            }
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          sx={{
            borderRadius: '12px',
            background: "linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)",
            fontWeight: 700,
            padding: '12px 32px',
            fontSize: '16px',
            boxShadow: '0 4px 12px rgba(46, 192, 203, 0.3)',
            '&:hover': {
              background: "linear-gradient(135deg, #23A3AD 0%, #2EC0CB 100%)",
              boxShadow: '0 6px 16px rgba(46, 192, 203, 0.4)',
              transform: 'translateY(-1px)'
            }
          }}
        >
          🎫 Raise Support Ticket
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ContactUsModal;
