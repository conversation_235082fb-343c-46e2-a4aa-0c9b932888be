import { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  IconButton,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Chip,
} from "@mui/material";
import { Close, AttachFile } from "@mui/icons-material";

const RaiseTicketModal = ({ open, onClose }) => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    countryCode: "+1",
    subject: [],
    description: "",
    attachments: []
  });

  const subjectOptions = [
    "Login Issues",
    "Password Reset",
    "Account Access",
    "Technical Support",
    "Billing Inquiry",
    "Feature Request",
    "Bug Report",
    "Other"
  ];

  const countryCodes = [
    { code: "+1", country: "US/CA" },
    { code: "+44", country: "UK" },
    { code: "+91", country: "IN" },
    { code: "+86", country: "CN" },
    { code: "+49", country: "DE" },
    { code: "+33", country: "FR" },
    { code: "+81", country: "JP" },
    { code: "+61", country: "AU" }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubjectChange = (event) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      subject: typeof value === 'string' ? value.split(',') : value
    }));
  };

  const handleSubjectDelete = (subjectToDelete) => {
    setFormData(prev => ({
      ...prev,
      subject: prev.subject.filter(s => s !== subjectToDelete)
    }));
  };

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...files]
    }));
  };

  const handleSubmit = () => {
    console.log("Ticket Data:", formData);
    alert("Ticket submitted successfully! We'll get back to you soon.");
    onClose();
    // Reset form
    setFormData({
      name: "",
      email: "",
      phone: "",
      countryCode: "+1",
      subject: [],
      description: "",
      attachments: []
    });
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: "16px",
          maxHeight: "90vh"
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        background: "linear-gradient(to right, #2EC0CB, #23A3AD)",
        color: "white",
        fontFamily: "'HCLTechRoobert', sans-serif"
      }}>
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          Raise Support Ticket
        </Typography>
        <IconButton onClick={onClose} sx={{ color: "white" }}>
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 1 }}>
          {/* Name Field */}
          <TextField
            label="Full Name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            fullWidth
            required
            sx={{
              '& .MuiOutlinedInput-root': {
                '&.Mui-focused fieldset': {
                  borderColor: '#2EC0CB',
                },
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: '#2EC0CB',
              },
            }}
          />

          {/* Email Field */}
          <TextField
            label="Email Address"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            fullWidth
            required
            sx={{
              '& .MuiOutlinedInput-root': {
                '&.Mui-focused fieldset': {
                  borderColor: '#2EC0CB',
                },
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: '#2EC0CB',
              },
            }}
          />

          {/* Phone Number with Country Code */}
          <Box sx={{ display: 'flex', gap: 1 }}>
            <FormControl sx={{ minWidth: 120 }}>
              <InputLabel>Country</InputLabel>
              <Select
                value={formData.countryCode}
                onChange={(e) => handleInputChange('countryCode', e.target.value)}
                label="Country"
                sx={{
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#2EC0CB',
                  },
                }}
              >
                {countryCodes.map((country) => (
                  <MenuItem key={country.code} value={country.code}>
                    {country.code} ({country.country})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              label="Phone Number"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              fullWidth
              sx={{
                '& .MuiOutlinedInput-root': {
                  '&.Mui-focused fieldset': {
                    borderColor: '#2EC0CB',
                  },
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: '#2EC0CB',
                },
              }}
            />
          </Box>

          {/* Subject Multi-Select */}
          <FormControl fullWidth>
            <InputLabel>Subject</InputLabel>
            <Select
              multiple
              value={formData.subject}
              onChange={handleSubjectChange}
              label="Subject"
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => (
                    <Chip 
                      key={value} 
                      label={value} 
                      size="small"
                      onDelete={() => handleSubjectDelete(value)}
                      sx={{ 
                        backgroundColor: '#E0F7F9',
                        color: '#2EC0CB',
                        '& .MuiChip-deleteIcon': {
                          color: '#2EC0CB'
                        }
                      }}
                    />
                  ))}
                </Box>
              )}
              sx={{
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#2EC0CB',
                },
              }}
            >
              {subjectOptions.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Description */}
          <TextField
            label="Description"
            multiline
            rows={4}
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            fullWidth
            placeholder="Please describe your issue in detail..."
            sx={{
              '& .MuiOutlinedInput-root': {
                '&.Mui-focused fieldset': {
                  borderColor: '#2EC0CB',
                },
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: '#2EC0CB',
              },
            }}
          />

          {/* File Upload */}
          <Box>
            <input
              accept="*/*"
              style={{ display: 'none' }}
              id="file-upload"
              multiple
              type="file"
              onChange={handleFileUpload}
            />
            <label htmlFor="file-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={<AttachFile />}
                sx={{
                  borderColor: '#2EC0CB',
                  color: '#2EC0CB',
                  '&:hover': {
                    borderColor: '#23A3AD',
                    backgroundColor: '#E0F7F9'
                  }
                }}
              >
                Attach Files
              </Button>
            </label>
            {formData.attachments.length > 0 && (
              <Box sx={{ mt: 1 }}>
                {formData.attachments.map((file, index) => (
                  <Chip
                    key={index}
                    label={file.name}
                    onDelete={() => {
                      setFormData(prev => ({
                        ...prev,
                        attachments: prev.attachments.filter((_, i) => i !== index)
                      }));
                    }}
                    sx={{ mr: 1, mb: 1 }}
                  />
                ))}
              </Box>
            )}
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button 
          onClick={onClose}
          sx={{ 
            color: '#666',
            '&:hover': {
              backgroundColor: '#f5f5f5'
            }
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          sx={{
            background: "linear-gradient(to right, #2EC0CB, #23A3AD)",
            fontWeight: 600,
            px: 3,
            '&:hover': {
              background: "linear-gradient(to right, #23A3AD, #2EC0CB)",
            }
          }}
        >
          Submit Ticket
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default RaiseTicketModal;
