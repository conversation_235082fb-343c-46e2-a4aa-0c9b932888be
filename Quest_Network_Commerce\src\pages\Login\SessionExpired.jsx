import React from "react";
import { Link as RouterLink } from "react-router-dom";
import {
  Box,
  Typography,
  Button,
  Paper,
  Container
} from "@mui/material";
import AuthLayout from "../../components/Login/AuthLayout";

const SessionExpired = () => {
  return (
    <AuthLayout title="Session Expired" subtitle="You have been logged out due to inactivity.">
      <div className="space-y-6 text-center">
        <Button
          variant="contained"
          component={RouterLink}
          to="/login"
          fullWidth
          sx={{
            background: "linear-gradient(to right, #2EC0CB, #23A3AD)",
            fontWeight: "bold",
            py: 1.5,
            fontSize: "1rem",
            borderRadius: "12px",
            textTransform: "none",
            ":hover": {
              background: "linear-gradient(to right, #23A3AD, #2EC0CB)",
            },
          }}
        >
          Login Again
        </Button>
      </div>
    </AuthLayout>
  );
};

export default SessionExpired;
