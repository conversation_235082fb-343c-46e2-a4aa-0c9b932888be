import React from "react";
import {
  Dialog,
  DialogTitle,
  <PERSON>alogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";

const SessionTimeoutModal = ({ onStay, onLogout }) => {
  return (
    <Dialog open onClose={onStay}>
      <DialogTitle>Session Expiring</DialogTitle>
      <DialogContent>
        <DialogContentText>
          You have been inactive for a while. Your session is about to expire. Do you want to stay logged in?
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={onLogout} color="error" variant="outlined">
          Logout
        </Button>
        <Button onClick={onStay} color="primary" variant="contained" autoFocus>
          Stay Logged In
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SessionTimeoutModal;
