import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Rating,
  Stack,
  Card,
  CardMedia,
  CardContent,
  Divider,
  Tooltip,
  useTheme,
  useMediaQuery,
  Tabs,
  Tab,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  Avatar,
  Badge
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import StarIcon from '@mui/icons-material/Star';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import SearchIcon from '@mui/icons-material/Search';
import AddIcon from '@mui/icons-material/Add';
import FilterListIcon from '@mui/icons-material/FilterList';
import CategoryIcon from '@mui/icons-material/Category';
import BrandingWatermarkIcon from '@mui/icons-material/BrandingWatermark';

import { masterProductList } from '../../data/masterProductList';
import {
  comparisonData,
  comparisonTemplates,
  comparisonScoring,
  similarProducts,
  comparisonSuggestions,
  comparisonCategories,
  brandComparisons,
  comparisonPresets
} from '../../data/productComparison';

function ProductComparison({ open, onClose, initialProducts = [] }) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [compareProducts, setCompareProducts] = useState([]);
  const [comparisonScores, setComparisonScores] = useState({});
  const [showProductPicker, setShowProductPicker] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedBrand, setSelectedBrand] = useState('all');
  const [currentProduct, setCurrentProduct] = useState(null);
  const [suggestions, setSuggestions] = useState({});
  const [activeTab, setActiveTab] = useState(0); // 0: Comparison, 1: Add Products

  useEffect(() => {
    if (initialProducts.length > 0) {
      const products = initialProducts.map(id =>
        masterProductList.find(p => p.id === id)
      ).filter(Boolean);
      setCompareProducts(products);
      calculateScores(products);

      // Set current product for suggestions
      if (products.length > 0) {
        setCurrentProduct(products[0]);
        loadSuggestions(products[0].id);
      }
    }
  }, [initialProducts]);

  const loadSuggestions = (productId) => {
    const productSuggestions = comparisonSuggestions[productId] || {};
    const similarProductData = similarProducts[productId] || {};

    setSuggestions({
      ...productSuggestions,
      similar: similarProductData.similar || [],
      alternatives: similarProductData.alternatives || [],
      otherBrands: similarProductData.otherBrands || []
    });
  };

  const calculateScores = (products) => {
    if (products.length < 2) return;

    const scores = {};
    Object.entries(comparisonScoring).forEach(([criterion, config]) => {
      const results = config.calculate(products);
      results.forEach((result, index) => {
        if (!scores[products[index].id]) scores[products[index].id] = {};
        scores[products[index].id][criterion] = result;
      });
    });

    // Calculate overall scores
    Object.keys(scores).forEach(productId => {
      const productScores = scores[productId];
      const overallScore = Object.entries(comparisonScoring).reduce((total, [criterion, config]) => {
        return total + (productScores[criterion]?.score || 0) * config.weight;
      }, 0);
      scores[productId].overall = { score: overallScore, reason: "Overall Score" };
    });

    setComparisonScores(scores);
  };

  const removeProduct = (productId) => {
    const updatedProducts = compareProducts.filter(p => p.id !== productId);
    setCompareProducts(updatedProducts);
    calculateScores(updatedProducts);
  };

  const addProduct = (productId) => {
    const product = masterProductList.find(p => p.id === productId);
    if (product && !compareProducts.find(p => p.id === productId)) {
      const updatedProducts = [...compareProducts, product];
      setCompareProducts(updatedProducts);
      calculateScores(updatedProducts);
    }
  };

  const addProductsFromPreset = (presetKey) => {
    const preset = comparisonPresets[presetKey];
    if (preset) {
      const products = preset.products.map(id =>
        masterProductList.find(p => p.id === id)
      ).filter(Boolean);
      setCompareProducts(products);
      calculateScores(products);
    }
  };

  const getFilteredProducts = () => {
    let filtered = masterProductList.filter(product => {
      // Search filter
      if (searchQuery) {
        const searchLower = searchQuery.toLowerCase();
        const matchesName = product.name?.en?.toLowerCase().includes(searchLower);
        const matchesBrand = product.brand?.en?.toLowerCase().includes(searchLower);
        const matchesPartNumber = product.partNumber?.toLowerCase().includes(searchLower);
        if (!matchesName && !matchesBrand && !matchesPartNumber) return false;
      }

      // Category filter
      if (selectedCategory !== 'all') {
        const categoryProducts = comparisonCategories[selectedCategory]?.products || [];
        if (!categoryProducts.includes(product.id)) return false;
      }

      // Brand filter
      if (selectedBrand !== 'all') {
        const brandProducts = brandComparisons[selectedBrand]?.products || [];
        if (!brandProducts.includes(product.id)) return false;
      }

      return true;
    });

    // Exclude already selected products
    filtered = filtered.filter(product =>
      !compareProducts.find(p => p.id === product.id)
    );

    return filtered;
  };

  const getFieldValue = (product, field) => {
    const keys = field.split('.');
    let value = product;
    for (const key of keys) {
      value = value?.[key];
      if (value === undefined) break;
    }
    return value;
  };

  const renderFieldValue = (value, type) => {
    if (value === undefined || value === null) return '-';
    
    switch (type) {
      case 'currency':
        return `$${value}`;
      case 'rating':
        return <Rating value={value} readOnly size="small" />;
      case 'array':
        return Array.isArray(value) ? (
          <Stack direction="row" spacing={0.5} flexWrap="wrap">
            {value.map((item, index) => (
              <Chip key={index} label={item} size="small" variant="outlined" />
            ))}
          </Stack>
        ) : value;
      case 'boolean':
        return value ? <CheckCircleIcon color="success" /> : <CancelIcon color="error" />;
      case 'compatibility':
        return Array.isArray(value) ? (
          <Typography variant="body2">
            {value.length} vehicles compatible
          </Typography>
        ) : '-';
      default:
        return value.toString();
    }
  };

  const getBestInCategory = (field, type) => {
    if (compareProducts.length === 0) return null;
    
    const values = compareProducts.map(p => getFieldValue(p, field));
    
    switch (type) {
      case 'currency':
        return Math.min(...values.filter(v => v !== undefined));
      case 'rating':
        return Math.max(...values.filter(v => v !== undefined));
      case 'array':
        const lengths = values.map(v => Array.isArray(v) ? v.length : 0);
        return Math.max(...lengths);
      default:
        return null;
    }
  };

  const isHighlighted = (product, field, type) => {
    const value = getFieldValue(product, field);
    const best = getBestInCategory(field, type);
    
    if (best === null || value === undefined) return false;
    
    switch (type) {
      case 'currency':
        return value === best;
      case 'rating':
        return value === best;
      case 'array':
        return Array.isArray(value) && value.length === best;
      default:
        return false;
    }
  };

  if (compareProducts.length === 0) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
        <DialogTitle>Product Comparison</DialogTitle>
        <DialogContent>
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <CompareArrowsIcon sx={{ fontSize: 64, color: 'grey.400', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No products to compare
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Add products to comparison from the product pages
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Close</Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="xl" fullWidth fullScreen={isMobile}>
      <DialogTitle>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h6">Product Comparison</Typography>
            <Typography variant="caption" color="text.secondary">
              {compareProducts.length} products selected
            </Typography>
          </Box>
          <Stack direction="row" spacing={1}>
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={() => setActiveTab(1)}
              size="small"
            >
              Add Products
            </Button>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Stack>
        </Stack>

        {/* Tabs */}
        <Tabs
          value={activeTab}
          onChange={(_, newValue) => setActiveTab(newValue)}
          sx={{ mt: 2 }}
        >
          <Tab label="Comparison" />
          <Tab label="Add Products" />
        </Tabs>
      </DialogTitle>
      
      <DialogContent sx={{ p: 0 }}>
        {/* Comparison Tab */}
        {activeTab === 0 && (
          <>
            {/* Product Cards Header */}
            <Box sx={{ p: 3, bgcolor: 'grey.50' }}>
          <Stack direction={{ xs: 'column', md: 'row' }} spacing={2}>
            {compareProducts.map((product) => (
              <Card key={product.id} sx={{ flex: 1, position: 'relative' }}>
                <IconButton
                  onClick={() => removeProduct(product.id)}
                  sx={{ position: 'absolute', top: 8, right: 8, zIndex: 1 }}
                  size="small"
                >
                  <CloseIcon />
                </IconButton>
                
                <CardMedia
                  component="img"
                  height="120"
                  image={product.imageUrl}
                  alt={product.name.en}
                  sx={{ objectFit: 'contain', bgcolor: 'white' }}
                />
                
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="subtitle1" fontWeight={600} gutterBottom>
                    {product.name.en}
                  </Typography>
                  <Typography variant="h6" color="primary.main" fontWeight={700}>
                    ${product.priceUSD}
                  </Typography>
                  <Rating value={product.rating || 0} readOnly size="small" sx={{ mt: 1 }} />
                  
                  {/* Overall Score */}
                  {comparisonScores[product.id]?.overall && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="caption" color="text.secondary">
                        Overall Score
                      </Typography>
                      <Typography variant="h6" color="primary.main">
                        {comparisonScores[product.id].overall.score.toFixed(0)}%
                      </Typography>
                    </Box>
                  )}
                </CardContent>
              </Card>
            ))}
          </Stack>
        </Box>

        {/* Comparison Table */}
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 600, bgcolor: 'grey.100' }}>
                  Specification
                </TableCell>
                {compareProducts.map((product) => (
                  <TableCell key={product.id} align="center" sx={{ bgcolor: 'grey.100' }}>
                    <Typography variant="subtitle2" fontWeight={600}>
                      {product.brand?.en}
                    </Typography>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            
            <TableBody>
              {/* Basic Information */}
              <TableRow>
                <TableCell colSpan={compareProducts.length + 1} sx={{ bgcolor: 'primary.50' }}>
                  <Typography variant="subtitle1" fontWeight={600} color="primary.main">
                    Basic Information
                  </Typography>
                </TableCell>
              </TableRow>
              
              {comparisonTemplates.automotive.basicInfo.map((spec) => (
                <TableRow key={spec.field}>
                  <TableCell sx={{ fontWeight: 600 }}>{spec.label}</TableCell>
                  {compareProducts.map((product) => {
                    const value = getFieldValue(product, spec.field);
                    const highlighted = isHighlighted(product, spec.field, spec.type);
                    return (
                      <TableCell 
                        key={product.id} 
                        align="center"
                        sx={{ 
                          bgcolor: highlighted ? 'success.50' : 'inherit',
                          fontWeight: highlighted ? 600 : 400
                        }}
                      >
                        {renderFieldValue(value, spec.type)}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))}

              {/* Specifications */}
              <TableRow>
                <TableCell colSpan={compareProducts.length + 1} sx={{ bgcolor: 'primary.50' }}>
                  <Typography variant="subtitle1" fontWeight={600} color="primary.main">
                    Specifications
                  </Typography>
                </TableCell>
              </TableRow>
              
              {comparisonTemplates.automotive.specifications.map((spec) => (
                <TableRow key={spec.field}>
                  <TableCell sx={{ fontWeight: 600 }}>{spec.label}</TableCell>
                  {compareProducts.map((product) => {
                    const value = getFieldValue(product, spec.field);
                    return (
                      <TableCell key={product.id} align="center">
                        {renderFieldValue(value, spec.type)}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))}

              {/* Quality & Certifications */}
              <TableRow>
                <TableCell colSpan={compareProducts.length + 1} sx={{ bgcolor: 'primary.50' }}>
                  <Typography variant="subtitle1" fontWeight={600} color="primary.main">
                    Quality & Certifications
                  </Typography>
                </TableCell>
              </TableRow>
              
              {comparisonTemplates.automotive.quality.map((spec) => (
                <TableRow key={spec.field}>
                  <TableCell sx={{ fontWeight: 600 }}>{spec.label}</TableCell>
                  {compareProducts.map((product) => {
                    const value = getFieldValue(product, spec.field);
                    const highlighted = isHighlighted(product, spec.field, spec.type);
                    return (
                      <TableCell 
                        key={product.id} 
                        align="center"
                        sx={{ 
                          bgcolor: highlighted ? 'success.50' : 'inherit',
                          fontWeight: highlighted ? 600 : 400
                        }}
                      >
                        {renderFieldValue(value, spec.type)}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
          </>
        )}

        {/* Add Products Tab */}
        {activeTab === 1 && (
          <Box sx={{ p: 3 }}>
            {/* Quick Suggestions */}
            {currentProduct && suggestions && Object.keys(suggestions).length > 0 && (
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" gutterBottom>
                  Suggested Products for {currentProduct.name?.en}
                </Typography>

                {Object.entries(suggestions).map(([category, productIds]) => {
                  if (!Array.isArray(productIds) || productIds.length === 0) return null;

                  const products = productIds.map(id =>
                    masterProductList.find(p => p.id === id)
                  ).filter(Boolean);

                  if (products.length === 0) return null;

                  return (
                    <Box key={category} sx={{ mb: 3 }}>
                      <Typography variant="subtitle1" gutterBottom color="primary.main">
                        {category}
                      </Typography>
                      <Grid container spacing={2}>
                        {products.slice(0, 4).map((product) => (
                          <Grid item xs={12} sm={6} md={3} key={product.id}>
                            <Card
                              sx={{
                                cursor: 'pointer',
                                '&:hover': { boxShadow: 4 }
                              }}
                              onClick={() => addProduct(product.id)}
                            >
                              <CardMedia
                                component="img"
                                height="80"
                                image={product.imageUrl}
                                alt={product.name?.en}
                                sx={{ objectFit: 'contain', bgcolor: 'grey.50' }}
                              />
                              <CardContent sx={{ p: 1.5 }}>
                                <Typography variant="caption" sx={{
                                  display: '-webkit-box',
                                  WebkitLineClamp: 2,
                                  WebkitBoxOrient: 'vertical',
                                  overflow: 'hidden'
                                }}>
                                  {product.name?.en}
                                </Typography>
                                <Typography variant="subtitle2" color="primary.main">
                                  ${product.priceUSD}
                                </Typography>
                              </CardContent>
                            </Card>
                          </Grid>
                        ))}
                      </Grid>
                    </Box>
                  );
                })}
              </Box>
            )}

            {/* Comparison Presets */}
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" gutterBottom>Quick Comparisons</Typography>
              <Grid container spacing={2}>
                {Object.entries(comparisonPresets).map(([key, preset]) => (
                  <Grid item xs={12} sm={6} md={4} key={key}>
                    <Card
                      sx={{
                        cursor: 'pointer',
                        '&:hover': { boxShadow: 4 }
                      }}
                      onClick={() => addProductsFromPreset(key)}
                    >
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          {preset.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {preset.description}
                        </Typography>
                        <Chip label={`${preset.products.length} products`} size="small" />
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>

            {/* Manual Product Search */}
            <Box>
              <Typography variant="h6" gutterBottom>Search Products</Typography>

              {/* Search and Filters */}
              <Stack direction={{ xs: 'column', md: 'row' }} spacing={2} sx={{ mb: 3 }}>
                <TextField
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ flex: 1 }}
                />

                <FormControl sx={{ minWidth: 150 }}>
                  <InputLabel>Category</InputLabel>
                  <Select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                  >
                    <MenuItem value="all">All Categories</MenuItem>
                    {Object.entries(comparisonCategories).map(([key, category]) => (
                      <MenuItem key={key} value={key}>{category.name}</MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <FormControl sx={{ minWidth: 150 }}>
                  <InputLabel>Brand</InputLabel>
                  <Select
                    value={selectedBrand}
                    onChange={(e) => setSelectedBrand(e.target.value)}
                  >
                    <MenuItem value="all">All Brands</MenuItem>
                    {Object.entries(brandComparisons).map(([key, brand]) => (
                      <MenuItem key={key} value={key}>{brand.name}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Stack>

              {/* Product Results */}
              <Grid container spacing={2}>
                {getFilteredProducts().slice(0, 12).map((product) => (
                  <Grid item xs={12} sm={6} md={4} lg={3} key={product.id}>
                    <Card
                      sx={{
                        cursor: 'pointer',
                        position: 'relative',
                        '&:hover': { boxShadow: 4 }
                      }}
                      onClick={() => addProduct(product.id)}
                    >
                      <CardMedia
                        component="img"
                        height="100"
                        image={product.imageUrl}
                        alt={product.name?.en}
                        sx={{ objectFit: 'contain', bgcolor: 'grey.50' }}
                      />
                      <CardContent>
                        <Typography variant="body2" sx={{
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          mb: 1
                        }}>
                          {product.name?.en}
                        </Typography>
                        <Typography variant="subtitle2" color="primary.main">
                          ${product.priceUSD}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {product.brand?.en}
                        </Typography>
                      </CardContent>
                      <IconButton
                        sx={{
                          position: 'absolute',
                          top: 8,
                          right: 8,
                          bgcolor: 'primary.main',
                          color: 'white',
                          '&:hover': { bgcolor: 'primary.dark' }
                        }}
                        size="small"
                      >
                        <AddIcon />
                      </IconButton>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Box>
        )}
      </DialogContent>
      
      <DialogActions sx={{ p: 3 }}>
        <Stack direction="row" spacing={2} sx={{ width: '100%' }}>
          <Button onClick={onClose} variant="outlined">
            Close
          </Button>

          {activeTab === 1 && (
            <Button
              onClick={() => setActiveTab(0)}
              variant="contained"
              disabled={compareProducts.length === 0}
            >
              View Comparison ({compareProducts.length})
            </Button>
          )}

          {activeTab === 0 && (
            <>
              <Button
                variant="outlined"
                onClick={() => {
                  setCompareProducts([]);
                  setComparisonScores({});
                }}
                disabled={compareProducts.length === 0}
              >
                Clear All
              </Button>

              <Button
                variant="contained"
                disabled={compareProducts.length === 0}
              >
                Save Comparison
              </Button>

              <Button
                variant="outlined"
                disabled={compareProducts.length === 0}
              >
                Share
              </Button>
            </>
          )}
        </Stack>
      </DialogActions>
    </Dialog>
  );
}

export default ProductComparison;
