export const comparisonData = {
  // Comparison groups by category
  brakes: {
    category: "Brake Components",
    products: [1, 3, 5], // Product IDs that can be compared
    comparisonFields: [
      {
        field: "priceUSD",
        label: "Price",
        type: "currency",
        important: true
      },
      {
        field: "material.en",
        label: "Material",
        type: "text",
        important: true
      },
      {
        field: "dimensions",
        label: "Dimensions",
        type: "text",
        important: false
      },
      {
        field: "weight",
        label: "Weight",
        type: "text",
        important: false
      },
      {
        field: "warranty.en",
        label: "Warranty",
        type: "text",
        important: true
      },
      {
        field: "certifications",
        label: "Certifications",
        type: "array",
        important: true
      },
      {
        field: "vehicleCompatibility",
        label: "Vehicle Compatibility",
        type: "compatibility",
        important: true
      },
      {
        field: "rating",
        label: "Customer Rating",
        type: "rating",
        important: true
      }
    ]
  },
  filters: {
    category: "Filters",
    products: [2, 4, 6], // Product IDs that can be compared
    comparisonFields: [
      {
        field: "priceUSD",
        label: "Price",
        type: "currency",
        important: true
      },
      {
        field: "dimensions",
        label: "Dimensions",
        type: "text",
        important: false
      },
      {
        field: "weight",
        label: "Weight",
        type: "text",
        important: false
      },
      {
        field: "material.en",
        label: "Filter Media",
        type: "text",
        important: true
      },
      {
        field: "certifications",
        label: "Certifications",
        type: "array",
        important: true
      },
      {
        field: "vehicleCompatibility",
        label: "Vehicle Compatibility",
        type: "compatibility",
        important: true
      },
      {
        field: "rating",
        label: "Customer Rating",
        type: "rating",
        important: true
      }
    ]
  }
};

// Comparison templates for different product types
export const comparisonTemplates = {
  automotive: {
    basicInfo: [
      { field: "name.en", label: "Product Name", type: "text" },
      { field: "brand.en", label: "Brand", type: "text" },
      { field: "partNumber", label: "Part Number", type: "text" },
      { field: "priceUSD", label: "Price", type: "currency" }
    ],
    specifications: [
      { field: "dimensions", label: "Dimensions", type: "text" },
      { field: "weight", label: "Weight", type: "text" },
      { field: "material.en", label: "Material", type: "text" },
      { field: "countryOfOrigin.en", label: "Country of Origin", type: "text" }
    ],
    quality: [
      { field: "certifications", label: "Certifications", type: "array" },
      { field: "warranty.en", label: "Warranty", type: "text" },
      { field: "rating", label: "Customer Rating", type: "rating" },
      { field: "condition.en", label: "Condition", type: "text" }
    ],
    compatibility: [
      { field: "vehicleCompatibility", label: "Compatible Vehicles", type: "compatibility" },
      { field: "oemReference", label: "OEM Reference", type: "text" }
    ],
    identification: [
      { field: "upc", label: "UPC", type: "text" },
      { field: "ean", label: "EAN", type: "text" },
      { field: "rfid.enabled", label: "RFID Enabled", type: "boolean" }
    ]
  }
};

// Comparison scoring system
export const comparisonScoring = {
  price: {
    weight: 0.3,
    calculate: (products) => {
      const prices = products.map(p => p.priceUSD);
      const minPrice = Math.min(...prices);
      return products.map(p => ({
        score: (minPrice / p.priceUSD) * 100,
        reason: p.priceUSD === minPrice ? "Best Price" : "Higher Price"
      }));
    }
  },
  rating: {
    weight: 0.25,
    calculate: (products) => {
      const maxRating = Math.max(...products.map(p => p.rating || 0));
      return products.map(p => ({
        score: ((p.rating || 0) / maxRating) * 100,
        reason: p.rating === maxRating ? "Highest Rated" : "Lower Rating"
      }));
    }
  },
  warranty: {
    weight: 0.2,
    calculate: (products) => {
      const warrantyValues = products.map(p => {
        const warranty = p.warranty?.en || "No warranty";
        if (warranty.includes("Lifetime")) return 100;
        if (warranty.includes("5 year")) return 90;
        if (warranty.includes("3 year")) return 80;
        if (warranty.includes("2 year")) return 70;
        if (warranty.includes("1 year")) return 60;
        return 30;
      });
      const maxWarranty = Math.max(...warrantyValues);
      return warrantyValues.map(w => ({
        score: (w / maxWarranty) * 100,
        reason: w === maxWarranty ? "Best Warranty" : "Limited Warranty"
      }));
    }
  },
  certifications: {
    weight: 0.15,
    calculate: (products) => {
      const certCounts = products.map(p => (p.certifications || []).length);
      const maxCerts = Math.max(...certCounts);
      return certCounts.map(count => ({
        score: maxCerts > 0 ? (count / maxCerts) * 100 : 100,
        reason: count === maxCerts ? "Most Certified" : "Fewer Certifications"
      }));
    }
  },
  compatibility: {
    weight: 0.1,
    calculate: (products) => {
      const compatCounts = products.map(p => (p.vehicleCompatibility || []).length);
      const maxCompat = Math.max(...compatCounts);
      return compatCounts.map(count => ({
        score: maxCompat > 0 ? (count / maxCompat) * 100 : 100,
        reason: count === maxCompat ? "Most Compatible" : "Limited Compatibility"
      }));
    }
  }
};

// Comparison highlights and recommendations
export const comparisonHighlights = {
  bestValue: {
    title: "Best Value",
    description: "Best combination of price and features",
    icon: "star"
  },
  bestPrice: {
    title: "Best Price",
    description: "Lowest price option",
    icon: "attach_money"
  },
  bestRated: {
    title: "Best Rated",
    description: "Highest customer rating",
    icon: "thumb_up"
  },
  mostFeatures: {
    title: "Most Features",
    description: "Most certifications and compatibility",
    icon: "featured_play_list"
  },
  recommended: {
    title: "Recommended",
    description: "Our top recommendation",
    icon: "recommend"
  }
};

// Similar products and alternatives by product ID
export const similarProducts = {
  1: { // Brake Pads
    similar: [3, 5, 7], // Similar brake pads
    alternatives: [9, 11], // Alternative brake solutions
    otherBrands: [13, 15], // Same product, different brands
    category: "Brake Components",
    reason: "Similar brake pad specifications and compatibility"
  },
  2: { // Oil Filter
    similar: [4, 6, 8], // Similar oil filters
    alternatives: [10, 12], // Alternative filtration solutions
    otherBrands: [14, 16], // Same product, different brands
    category: "Filters",
    reason: "Compatible oil filters with similar specifications"
  }
};

// Product suggestions for comparison
export const comparisonSuggestions = {
  1: {
    "Frequently Compared": [3, 5],
    "Popular Alternatives": [7, 9],
    "Budget Options": [11, 13],
    "Premium Options": [15, 17],
    "Same Brand": [19, 21],
    "Other Brands": [23, 25]
  },
  2: {
    "Frequently Compared": [4, 6],
    "Popular Alternatives": [8, 10],
    "Budget Options": [12, 14],
    "Premium Options": [16, 18],
    "Same Brand": [20, 22],
    "Other Brands": [24, 26]
  }
};

// Comparison categories for filtering
export const comparisonCategories = {
  "Brake Components": {
    name: "Brake Components",
    products: [1, 3, 5, 7, 9, 11, 13, 15],
    icon: "brake_check",
    description: "Brake pads, rotors, and brake system components"
  },
  "Filters": {
    name: "Filters",
    products: [2, 4, 6, 8, 10, 12, 14, 16],
    icon: "filter_alt",
    description: "Oil filters, air filters, and filtration systems"
  },
  "Engine Components": {
    name: "Engine Components",
    products: [17, 18, 19, 20, 21, 22],
    icon: "precision_manufacturing",
    description: "Engine parts and performance components"
  },
  "Suspension": {
    name: "Suspension",
    products: [23, 24, 25, 26, 27, 28],
    icon: "settings_motion",
    description: "Shocks, struts, and suspension components"
  }
};

// Brand-based product grouping
export const brandComparisons = {
  "OEM": {
    name: "OEM Parts",
    products: [1, 2, 17, 23],
    description: "Original Equipment Manufacturer parts"
  },
  "Aftermarket Premium": {
    name: "Premium Aftermarket",
    products: [3, 4, 18, 24],
    description: "High-quality aftermarket alternatives"
  },
  "Budget Friendly": {
    name: "Budget Options",
    products: [5, 6, 19, 25],
    description: "Cost-effective replacement parts"
  },
  "Performance": {
    name: "Performance Parts",
    products: [7, 8, 20, 26],
    description: "Enhanced performance components"
  }
};

// Comparison presets for quick selection
export const comparisonPresets = {
  "brake_pads_premium": {
    name: "Premium Brake Pads",
    description: "Compare top-rated brake pad options",
    products: [1, 3, 7],
    category: "Brake Components"
  },
  "oil_filters_popular": {
    name: "Popular Oil Filters",
    description: "Most popular oil filter choices",
    products: [2, 4, 6],
    category: "Filters"
  },
  "budget_vs_premium": {
    name: "Budget vs Premium",
    description: "Compare budget and premium options",
    products: [5, 1, 7],
    category: "Mixed"
  },
  "brand_comparison": {
    name: "Brand Comparison",
    description: "Compare different brand options",
    products: [1, 13, 15],
    category: "Mixed"
  }
};

// Saved comparisons (user can save comparison sets)
export const savedComparisons = [
  {
    id: "comp_001",
    name: "Brake Pads Comparison",
    products: [1, 3, 5],
    dateCreated: "2024-02-01",
    category: "brakes",
    shared: false,
    notes: "Comparing ceramic vs metallic brake pads"
  },
  {
    id: "comp_002",
    name: "Oil Filters Comparison",
    products: [2, 4, 6],
    dateCreated: "2024-02-05",
    category: "filters",
    shared: true,
    notes: "Performance oil filter comparison"
  },
  {
    id: "comp_003",
    name: "Budget vs Premium Brakes",
    products: [1, 5, 7],
    dateCreated: "2024-02-10",
    category: "brakes",
    shared: false,
    notes: "Cost vs performance analysis"
  }
];
