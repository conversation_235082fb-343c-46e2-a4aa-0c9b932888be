// src/components/NotificationDetail.jsx
import React, { useEffect, useRef } from 'react';
import { getNotificationIconAndColor, getPriorityBadgeClass, formatRelativeTime } from '../Notification/helpers';
import { CheckCircle, Autorenew, VisibilityOff, Star, StarBorder, Archive, Unarchive, Delete, Schedule, MarkEmailRead, DoneAll, ArrowBack, ReceiptLong, RateReview, Payment, VpnKey, LocalOffer, AddShoppingCart, Settings, Chat, Info, Label } from '@mui/icons-material';

const NotificationDetail = React.memo(({ notification, onBack, onAction, onUpdateDetail, buttonFeedback = {} }) => {
    const hasMarkedReadByEffectRef = useRef(false);

    useEffect(() => {
        if (notification && notification.status === 'unread' && !notification.archived) {
            if (!hasMarkedReadByEffectRef.current) {
                onAction(notification.id, 'mark-read', { suppressToast: true });
                hasMarkedReadByEffectRef.current = true;
            }
        }
        return () => {
            hasMarkedReadByEffectRef.current = false;
        };
    }, [notification, onAction]);

    // Effect to listen for notification-updated event and force update NotificationDetail
    useEffect(() => {
        function handleNotificationUpdated(e) {
            if (notification && e.detail && e.detail.id === notification.id) {
                // Force update by getting latest notification from props (App will re-render)
                if (typeof window !== 'undefined' && window.requestAnimationFrame) {
                    window.requestAnimationFrame(() => {
                        // No-op: App will update selectedNotificationDetail
                    });
                }
            }
        }
        window.addEventListener('notification-updated', handleNotificationUpdated);
        return () => {
            window.removeEventListener('notification-updated', handleNotificationUpdated);
        };
    }, [notification]);

    if (!notification) {
        return (
            <div id="notification-detail-page" className="full-page-notification-section active">
                <div className="max-w-7xl mx-auto py-6 px-6 lg:px-8">
                    <div className="header-banner">
                        <button id="back-to-list-from-detail-btn" onClick={onBack}>
                            <ArrowBack fontSize="small" />
                            Back to All Notifications
                        </button>
                        <h2 id="detail-view-notification-type" className="text-xl font-bold text-white">Notification Not Found</h2>
                        <div className="w-1/4"></div>
                    </div>
                    <div id="notification-detail-view" className="bg-white rounded-lg shadow-lg p-6 text-center">
                        <p className="text-gray-500">The notification you are looking for does not exist or has been removed.</p>
                    </div>
                </div>
            </div>
        );
    }

    const { id, type, status, archived, starred, priority, title, message, details, timestamp, link } = notification;
    const { icon, color, border } = getNotificationIconAndColor(type);
    const priorityClass = getPriorityBadgeClass(priority);

    let contextualActionButton;
    if (type === 'cart') {
        contextualActionButton = (
            <a href={link} className="btn-primary" onClick={(e) => e.stopPropagation()}>
                <AddShoppingCart fontSize="small" /> Go to Cart
            </a>
        );
    } else if (type === 'order') {
        contextualActionButton = (
            <a href={link} className="btn-primary" onClick={(e) => e.stopPropagation()}>
                <ReceiptLong fontSize="small" /> View Order
            </a>
        );
    } else if (type === 'review') {
        contextualActionButton = (
            <a href={link} className="btn-primary" onClick={(e) => e.stopPropagation()}>
                <RateReview fontSize="small" /> Leave a Review
            </a>
        );
    } else if (type === 'payment') {
        contextualActionButton = (
            <a href={link} className="btn-primary" onClick={(e) => e.stopPropagation()}>
                <Payment fontSize="small" /> Make Payment
            </a>
        );
    } else if (type === 'security') {
        contextualActionButton = (
            <a href={link} className="btn-primary" onClick={(e) => e.stopPropagation()}>
                <VpnKey fontSize="small" /> Change Password
            </a>
        );
    } else if (type === 'promotion') {
        contextualActionButton = (
            <a href={link} className="btn-primary" onClick={(e) => e.stopPropagation()}>
                <LocalOffer fontSize="small" /> View Promotion
            </a>
        );
    } else if (type === 'stock') {
        contextualActionButton = (
            <a href={link} className="btn-primary" onClick={(e) => e.stopPropagation()}>
                <AddShoppingCart fontSize="small" /> Buy Now
            </a>
        );
    } else if (type === 'system') {
        contextualActionButton = (
            <a href={link} className="btn-primary" onClick={(e) => e.stopPropagation()}>
                <Settings fontSize="small" /> View System Status
            </a>
        );
    } else if (type === 'support') {
        contextualActionButton = (
            <a href={link} className="btn-primary" onClick={(e) => e.stopPropagation()}>
                <Chat fontSize="small" /> View Ticket
            </a>
        );
    }

    // Helper to get feedback state for a button
    const getBtnFeedback = (action) => buttonFeedback[`${notification?.id}-${action}`] || {};

    // --- Mark as Read/Unread button logic ---
    const isRead = status === 'read' || status === 'remind_later';
    const markBtnFeedback = getBtnFeedback(isRead ? 'mark-unread' : 'mark-read');
    const markButton = isRead ?
        <button
            className={`btn-primary flex items-center gap-1 ${markBtnFeedback.isLoading ? 'opacity-60 pointer-events-none' : ''}`}
            onClick={async e => {
                e.stopPropagation();
                await onAction(id, 'mark-unread', { buttonId: `${id}-mark-unread` });
                // Immediately update notification state for instant UI feedback
                if (typeof onUpdateDetail === 'function') {
                    onUpdateDetail();
                }
            }}
            aria-label="Mark as Unread"
            disabled={markBtnFeedback.isLoading}
            title="Mark as Unread"
        >
            {markBtnFeedback.isLoading ? <Autorenew className="animate-spin" fontSize="small" /> : <VisibilityOff fontSize="small" />}
            {markBtnFeedback.isSuccess ? <CheckCircle fontSize="small" className="text-green-600" /> : null}
            Mark as Unread
        </button>
        :
        <button
            className={`btn-primary flex items-center gap-1 ${markBtnFeedback.isLoading ? 'opacity-60 pointer-events-none' : ''}`}
            onClick={async e => {
                e.stopPropagation();
                await onAction(id, 'mark-read', { buttonId: `${id}-mark-read` });
                if (typeof onUpdateDetail === 'function') {
                    onUpdateDetail();
                }
            }}
            aria-label="Mark as Read"
            disabled={markBtnFeedback.isLoading}
            title="Mark as Read"
        >
            {markBtnFeedback.isLoading ? <Autorenew className="animate-spin" fontSize="small" /> : <CheckCircle fontSize="small" />}
            {markBtnFeedback.isSuccess ? <CheckCircle fontSize="small" className="text-green-600" /> : null}
            Mark as Read
        </button>;

    // --- Star/Unstar button ---
    const starBtnFeedback = getBtnFeedback('toggle-star');
    const starButtonClass = starred ? 'active' : '';
    const starButtonIcon = starred ? <Star fontSize="small" color="warning" /> : <StarBorder fontSize="small" color="action" />;
    const starButton = (
        <button
            className={`btn-primary flex items-center gap-1 ${starButtonClass} ${starBtnFeedback.isLoading ? 'opacity-60 pointer-events-none' : ''}`}
            onClick={async e => {
                e.stopPropagation();
                await onAction(id, 'toggle-star', { buttonId: `${id}-toggle-star` });
                if (typeof onUpdateDetail === 'function') {
                    onUpdateDetail();
                }
            }}
            aria-label={starred ? 'Unstar' : 'Star'}
            disabled={starBtnFeedback.isLoading}
            title={starred ? 'Unstar' : 'Star'}
        >
            {starBtnFeedback.isLoading ? <Autorenew fontSize="small" className="animate-spin" /> : starButtonIcon}
            {starBtnFeedback.isSuccess ? <CheckCircle fontSize="small" className="text-yellow-500" /> : null}
            {starred ? 'Unstar' : 'Star'}
        </button>
    );

    // --- Archive/Unarchive button ---
    const archiveBtnFeedback = getBtnFeedback(archived ? 'unarchive' : 'archive');
    const archiveButton = archived ? (
        <button
            className={`btn-primary flex items-center gap-1 ${archiveBtnFeedback.isLoading ? 'opacity-60 pointer-events-none' : ''}`}
            onClick={async e => {
                e.stopPropagation();
                await onAction(id, 'unarchive', { buttonId: `${id}-unarchive` });
                if (typeof onUpdateDetail === 'function') {
                    onUpdateDetail();
                }
            }}
            aria-label="Unarchive"
            disabled={archiveBtnFeedback.isLoading}
            title="Unarchive"
        >
            {archiveBtnFeedback.isLoading ? <Autorenew fontSize="small" className="animate-spin" /> : <Unarchive fontSize="small" color="primary" />}
            {archiveBtnFeedback.isSuccess ? <CheckCircle fontSize="small" className="text-green-600" /> : null}
            Unarchive
        </button>
    ) : (
        <button
            className={`btn-primary flex items-center gap-1 ${archiveBtnFeedback.isLoading ? 'opacity-60 pointer-events-none' : ''}`}
            onClick={async e => {
                e.stopPropagation();
                await onAction(id, 'archive', { buttonId: `${id}-archive` });
                if (typeof onUpdateDetail === 'function') {
                    onUpdateDetail();
                }
            }}
            aria-label="Archive"
            disabled={archiveBtnFeedback.isLoading}
            title="Archive"
        >
            {archiveBtnFeedback.isLoading ? <Autorenew fontSize="small" className="animate-spin" /> : <Archive fontSize="small" color="primary" />}
            {archiveBtnFeedback.isSuccess ? <CheckCircle fontSize="small" className="text-green-600" /> : null}
            Archive
        </button>
    );

    // --- Delete button ---
    const deleteBtnFeedback = getBtnFeedback('delete');
    const deleteButton = (
        <button
            className={`btn-secondary delete flex items-center gap-1 ${deleteBtnFeedback.isLoading ? 'opacity-60 pointer-events-none' : ''}`}
            onClick={e => { e.stopPropagation(); onAction(id, 'delete', { buttonId: `${id}-delete` }); }}
            aria-label="Delete"
            disabled={deleteBtnFeedback.isLoading}
            title="Delete"
        >
            {deleteBtnFeedback.isLoading ? <Autorenew className="animate-spin" fontSize="small" /> : <Delete fontSize="small" />}
            {deleteBtnFeedback.isSuccess ? <CheckCircle fontSize="small" className="text-green-600" /> : null}
            Delete
        </button>
    );

    // Dynamic class for header border based on notification type
    const headerBorderClass = getNotificationIconAndColor(type).border;
    const typeIndicatorColor = getNotificationIconAndColor(type).color;
    const TypeIcon = getNotificationIconAndColor(type).icon;


    return (
        <div id="notification-detail-page" className="full-page-notification-section active">
            <div className="max-w-7xl mx-auto py-6 px-6 lg:px-8 mt-32">
                <div className="header-banner">
                    <button id="back-to-list-from-detail-btn" onClick={onBack}>
                        <ArrowBack fontSize="small" />
                        Back to All Notifications
                    </button>
                    {/* Enhanced Type Indicator */}
                    <h2 id="detail-view-notification-type" className={`text-xl font-bold text-white flex items-center`}>
                        {TypeIcon && React.createElement(TypeIcon, { fontSize: 'small', className: typeIndicatorColor + ' mr-2' })}
                        {type.charAt(0).toUpperCase() + type.slice(1)} Notification
                    </h2>
                    <div className="w-1/4"></div>
                </div>

                <div id="notification-detail-view" className="bg-white rounded-lg shadow-lg p-6">
                    {/* Applied dynamic border color to header */}
                    <div className={`detail-header ${headerBorderClass}`}> {/* Re-added border class here */}
                        {/* Icon and title are now in one line for better flow */}
                        <div className="flex items-center gap-2">
                            {TypeIcon && (
                                <span className={`notification-type-icon flex items-center justify-center ${typeIndicatorColor}`}>
                                    {React.createElement(TypeIcon, { fontSize: 'inherit', style: { fontSize: 36, color: 'inherit' } })}
                                </span>
                            )}
                            <div className="detail-content-wrap flex flex-col">
                                <h1 className="detail-title text-base font-semibold flex items-center gap-2">
                                    {title}
                                    {/* Priority badge is visually distinct */}
                                    {priority && <span className={`priority-badge ${priorityClass} ml-2`}>{priority}</span>}
                                </h1>
                                <p className="detail-message text-sm text-gray-700">{message}</p>
                            </div>
                        </div>
                        <span className="detail-timestamp ml-auto text-xs text-gray-500">{formatRelativeTime(timestamp)}</span>
                    </div>
                    {/* Enhanced details section with clear heading */}
                    {details && (
                        <div className="detail-details flex flex-col items-start p-4 rounded-md mt-4" style={{ backgroundColor: 'var(--color-gray-50)', borderLeft: '3px solid var(--color-blue-100)' }}>
                            <h3 className="text-base font-semibold text-gray-700 mb-2 flex items-center">
                                <Info fontSize="small" className="mr-2 text-blue-600" />
                                Additional Details:
                            </h3>
                            <p className="text-gray-700 text-sm leading-relaxed">{details}</p>
                        </div>
                    )}
                    <div className="detail-actions flex flex-wrap gap-2 mt-4">
                        {contextualActionButton}
                        {markButton}
                        {starButton}
                        {archiveButton}
                        {deleteButton}
                    </div>
                </div>
            </div>
        </div>
    );
});

export default NotificationDetail;