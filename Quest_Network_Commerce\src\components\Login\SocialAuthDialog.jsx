import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  IconButton,
  Divider
} from '@mui/material';
import { Close } from '@mui/icons-material';

const SocialAuthDialog = ({ open, onClose, provider, icon }) => {
  const handleAuth = () => {
    // Simulate authentication process
    console.log(`Authenticating with ${provider}...`);
    // Here you would implement actual social auth logic
    onClose();
  };

  const getProviderColor = () => {
    switch (provider?.toLowerCase()) {
      case 'google':
        return '#4285f4';
      case 'facebook':
        return '#1877f2';
      case 'twitter':
        return '#1da1f2';
      case 'linkedin':
        return '#0077b5';
      case 'github':
        return '#333333';
      default:
        return '#2EC0CB';
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '16px',
          fontFamily: "'Roobert', sans-serif",
        }
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          pb: 1,
          fontFamily: "'Roobert', sans-serif",
          fontWeight: 600,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {icon && (
            <Box
              sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                backgroundColor: getProviderColor(),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
              }}
            >
              {icon}
            </Box>
          )}
          <Typography variant="h6" sx={{ fontFamily: "'Roobert', sans-serif" }}>
            Continue with {provider}
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <Close />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ py: 3 }}>
        <Typography
          variant="body1"
          sx={{
            mb: 2,
            fontFamily: "'Roobert', sans-serif",
            color: '#666',
            textAlign: 'center',
          }}
        >
          You'll be redirected to {provider} to complete the authentication process.
        </Typography>

        <Box
          sx={{
            p: 2,
            backgroundColor: '#f5f5f5',
            borderRadius: '8px',
            mb: 2,
          }}
        >
          <Typography
            variant="body2"
            sx={{
              fontFamily: "'Roobert', sans-serif",
              color: '#555',
              fontSize: '0.85rem',
            }}
          >
            <strong>What we'll access:</strong>
            <br />
            • Your basic profile information
            <br />
            • Your email address
            <br />
            • Your profile picture (optional)
          </Typography>
        </Box>

        <Typography
          variant="caption"
          sx={{
            fontFamily: "'Roobert', sans-serif",
            color: '#888',
            display: 'block',
            textAlign: 'center',
          }}
        >
          By continuing, you agree to our Terms of Service and Privacy Policy.
        </Typography>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3, gap: 1 }}>
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{
            borderColor: '#ddd',
            color: '#666',
            fontFamily: "'Roobert', sans-serif",
            textTransform: 'none',
            borderRadius: '8px',
            '&:hover': {
              borderColor: '#bbb',
              backgroundColor: '#f9f9f9',
            },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleAuth}
          variant="contained"
          sx={{
            backgroundColor: getProviderColor(),
            color: 'white',
            fontFamily: "'Roobert', sans-serif",
            textTransform: 'none',
            borderRadius: '8px',
            '&:hover': {
              backgroundColor: getProviderColor(),
              opacity: 0.9,
            },
          }}
        >
          Continue with {provider}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SocialAuthDialog;
