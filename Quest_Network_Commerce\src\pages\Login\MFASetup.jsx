import React from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import {
  Box,
  Button,
  Typography,
  Paper,
  Divider,
} from "@mui/material";

const MFASetup = () => {
  const navigate = useNavigate();

  const handleContinue = () => {
    navigate("/mfa-verify");
  };

  const handleBiometric = () => {
    alert("Biometric setup simulated.");
    navigate("/mfa-verify");
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      style={{
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background: "linear-gradient(to right, #2EC0CB, #23A3AD)",
        padding: 24,
      }}
    >
      <Paper
        elevation={6}
        sx={{
          p: 5,
          borderRadius: 6,
          maxWidth: 400,
          width: "100%",
          border: "2px solid #23A3AD",
        }}
      >
        <Typography
          variant="h6"
          fontWeight="bold"
          align="center"
          sx={{ color: "#2EC0CB", mb: 4 }}
        >
          Setup MFA
        </Typography>

        <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
          <Box
            sx={{
              width: 160,
              height: 160,
              borderRadius: 2,
              backgroundColor: "#e0e0e0",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              mb: 1,
            }}
          >
            <Typography variant="body2" color="textSecondary">
              QR Code
            </Typography>
          </Box>

          <Typography
            variant="body2"
            align="center"
            color="text.secondary"
            sx={{ px: 2 }}
          >
            Scan the QR code above using Google Authenticator or any OTP app.
          </Typography>
        </Box>

        <Box mt={4} display="flex" flexDirection="column" gap={2}>
          <Button
            variant="contained"
            fullWidth
            onClick={handleContinue}
            sx={{
              background: "linear-gradient(to right, #2EC0CB, #23A3AD)",
              py: 1.3,
              fontWeight: "bold",
              borderRadius: "12px",
              fontSize: "1rem",
              textTransform: "none",
              "&:hover": {
                background: "#23A3AD",
              },
            }}
          >
            I’ve Set It Up
          </Button>

          <Divider sx={{ my: 1.5 }}>or</Divider>

          <Button
            variant="outlined"
            fullWidth
            onClick={handleBiometric}
            sx={{
              py: 1.3,
              borderRadius: "12px",
              fontWeight: "bold",
              color: "#2EC0CB",
              borderColor: "#2EC0CB",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#f5f5f5",
                borderColor: "#23A3AD",
              },
            }}
          >
            Setup Face ID / Fingerprint
          </Button>
        </Box>

        <Typography
          variant="caption"
          align="center"
          display="block"
          sx={{ mt: 4, color: "text.secondary" }}
        >
          Having trouble?{" "}
          <span
            onClick={() => navigate("/mfa-verify")}
            style={{
              color: "#2EC0CB",
              textDecoration: "underline",
              cursor: "pointer",
            }}
          >
            Skip for now
          </span>
        </Typography>
      </Paper>
    </motion.div>
  );
};

export default MFASetup;
