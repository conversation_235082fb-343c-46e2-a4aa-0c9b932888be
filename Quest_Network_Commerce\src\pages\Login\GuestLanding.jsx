import React from "react";
import { Box, Typography, Paper } from "@mui/material";
import { Link } from "react-router-dom";

const GuestLanding = () => {
  return (
    <Box
      sx={{
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#f3f4f6", // same as Tailwind's gray-100
        p: 2,
      }}
    >
      <Paper
        elevation={6}
        sx={{
          textAlign: "center",
          p: 4,
          borderRadius: 4,
          maxWidth: 400,
        }}
      >
        <Typography
          variant="h5"
          fontWeight="bold"
          sx={{ color: "#2EC0CB", mb: 1 }}
        >
          Welcome, Guest!
        </Typography>
        <Typography variant="body2" color="text.secondary">
          You're browsing in guest mode with limited features.
        </Typography>
        <Typography
          variant="caption"
          color="text.disabled"
          sx={{ mt: 2, display: "block" }}
        >
          You can continue exploring or create an account anytime.
        </Typography>
      </Paper>
    </Box>
  );
};

export default GuestLanding;
