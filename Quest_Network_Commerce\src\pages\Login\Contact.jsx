import React from "react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import { Typography, Box } from "@mui/material";
import AuthLayout from "../../components/Login/AuthLayout";;

const Contact = () => {
  return (
    <AuthLayout title="Contact Us" subtitle="We're here to help you">
      <div className="space-y-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography 
              variant="h6" 
              sx={{ 
                color: '#2EC0CB', 
                mb: 3,
                fontWeight: 'bold'
              }}
            >
              Get in Touch
            </Typography>
            
            <Typography 
              sx={{ 
                color: '#666', 
                mb: 2,
                fontSize: '0.9rem'
              }}
            >
              Having trouble with your account or need assistance?
            </Typography>
            
            <Typography 
              sx={{ 
                color: '#666', 
                mb: 4,
                fontSize: '0.9rem'
              }}
            >
              Our support team is ready to help you.
            </Typography>

            <Box sx={{ 
              backgroundColor: '#f9f9f9', 
              borderRadius: '12px', 
              p: 3, 
              mb: 4,
              border: '2px solid #2EC0CB'
            }}>
              <Typography 
                sx={{ 
                  color: '#2EC0CB', 
                  fontWeight: 'bold',
                  mb: 2,
                  fontSize: '1rem'
                }}
              >
                Contact Information
              </Typography>
              
              <Typography sx={{ color: '#666', mb: 1, fontSize: '0.85rem' }}>
                📧 Email: <EMAIL>
              </Typography>
              
              <Typography sx={{ color: '#666', mb: 1, fontSize: '0.85rem' }}>
                📞 Phone: +****************
              </Typography>
              
              <Typography sx={{ color: '#666', mb: 1, fontSize: '0.85rem' }}>
                💬 Live Chat: Available 24/7
              </Typography>
              
              <Typography sx={{ color: '#666', fontSize: '0.85rem' }}>
                🕒 Support Hours: Mon-Fri 9AM-6PM EST
              </Typography>
            </Box>

            <Typography 
              sx={{ 
                color: '#666', 
                fontSize: '0.8rem',
                fontStyle: 'italic'
              }}
            >
              We typically respond within 24 hours
            </Typography>
          </Box>

          <Typography align="center" sx={{ mt: 3, fontSize: "0.85rem" }}>
            Having issues?{" "}
            <Link to="/contact" style={{ color: "#2EC0CB", fontWeight: 600 }}>
              Contact Us
            </Link>
            {" | "}
            <Link to="/login" style={{ color: "#2EC0CB", fontWeight: 600 }}>
              Login
            </Link>
          </Typography>
        </motion.div>
      </div>
    </AuthLayout>
  );
};

export default Contact;
