import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Box,
  Button,
  TextField,
  Typography,
  Paper,
  Container
} from "@mui/material";
import { motion } from "framer-motion";
import AuthLayout from "../../components/Login/AuthLayout";
import StyledTextField from "../../components/Login/StyledTextField";

const Verify = () => {
  const [otp, setOtp] = useState("");
  const navigate = useNavigate();

  const handleVerify = () => {
    if (otp === "123456") {
      sessionStorage.setItem("verified", "true");
      navigate("/register");
    } else {
      alert("Invalid OTP. Try again.");
    }
  };

  return (
    <AuthLayout title="Enter Verification Code" subtitle="We've sent a 6-digit code to verify your identity">
      <div className="space-y-6">
        <StyledTextField
          label="6-digit OTP"
          value={otp}
          onChange={(e) => setOtp(e.target.value)}
          placeholder="Enter verification code"
        />

        <Button
          variant="contained"
          fullWidth
          onClick={handleVerify}
          sx={{
            background: "linear-gradient(to right, #2EC0CB, #23A3AD)",
            fontWeight: "bold",
            py: 1.5,
            fontSize: "1rem",
            borderRadius: "12px",
            textTransform: "none",
            ":hover": {
              background: "linear-gradient(to right, #23A3AD, #2EC0CB)",
            },
          }}
        >
          Verify Code
        </Button>
      </div>
    </AuthLayout>
  );
};

export default Verify;
