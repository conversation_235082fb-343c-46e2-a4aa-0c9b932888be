// src/components/ConfirmModal.jsx
import React from 'react';

const ConfirmModal = React.memo(({ message, isOpen, onConfirm, onCancel }) => {
    if (!isOpen) return null;

    return (
        <div id="custom-confirm-modal" className={`fixed inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center z-50 transition-opacity duration-300 ${isOpen ? 'active' : ''}`}>
            <div className={`bg-white rounded-lg shadow-xl p-6 w-full max-w-sm transform transition-all duration-300 ${isOpen ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}`} id="custom-confirm-box">
                <p id="custom-confirm-message" className="text-lg text-gray-800 mb-6">{message}</p>
                <div className="flex justify-end space-x-4">
                    <button id="custom-confirm-cancel-btn" onClick={onCancel}>Cancel</button>
                    <button id="custom-confirm-ok-btn" onClick={onConfirm}>Confirm</button>
                </div>
            </div>
        </div>
    );
});

export default ConfirmModal;