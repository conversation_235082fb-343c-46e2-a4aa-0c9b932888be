import React from 'react';
import { Snackbar, Alert, AlertTitle } from '@mui/material';

const CustomSnackbar = ({ 
  open, 
  onClose, 
  message, 
  severity = 'info', 
  title = null,
  autoHideDuration = 6000 
}) => {
  return (
    <Snackbar
      open={open}
      autoHideDuration={autoHideDuration}
      onClose={onClose}
      anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      sx={{
        '& .MuiSnackbarContent-root': {
          fontFamily: "'Roobert', sans-serif",
        }
      }}
    >
      <Alert 
        onClose={onClose} 
        severity={severity}
        variant="filled"
        sx={{
          fontFamily: "'Roobert', sans-serif",
          '& .MuiAlert-message': {
            fontFamily: "'Roobert', sans-serif",
          },
          '& .MuiAlert-action': {
            fontFamily: "'Roobert', sans-serif",
          }
        }}
      >
        {title && <AlertTitle sx={{ fontFamily: "'Roo<PERSON>', sans-serif" }}>{title}</AlertTitle>}
        {message}
      </Alert>
    </Snackbar>
  );
};

export default CustomSnackbar;
