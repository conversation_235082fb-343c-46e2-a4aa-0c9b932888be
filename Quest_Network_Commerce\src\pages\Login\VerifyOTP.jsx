import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Box,
  Button,
  Typography,
  Paper,
  TextField,
  Container,
} from "@mui/material";
import AuthLayout from "../../components/Login/AuthLayout";

const VerifyOTP = () => {
  const [otp, setOtp] = useState(new Array(6).fill(""));
  const [timer, setTimer] = useState(60);
  const inputRefs = useRef([]);
  const navigate = useNavigate();
  const location = useLocation();
  const phone = location.state?.phone || "your number";

  useEffect(() => {
    inputRefs.current[0]?.focus();
  }, []);

  useEffect(() => {
    const countdown = setInterval(() => {
      setTimer((prev) => (prev > 0 ? prev - 1 : 0));
    }, 1000);
    return () => clearInterval(countdown);
  }, []);

  const handleChange = (e, index) => {
    const value = e.target.value.replace(/\D/, "");
    if (!value) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleSubmit = () => {
    const enteredOtp = otp.join("");
    console.log("Entered OTP:", enteredOtp);
    navigate("/dashboard");
  };

  const resendOTP = () => {
    console.log("Resending OTP to:", phone);
    setTimer(60);
  };

  return (
    <AuthLayout title="Verify OTP" subtitle={`Sent to ${phone}`}>
      <div className="space-y-6 text-center">

        <Box display="flex" justifyContent="center" gap={1.5} mb={4}>
          {otp.map((digit, index) => (
            <TextField
              key={index}
              inputRef={(el) => (inputRefs.current[index] = el)}
              type="text"
              inputProps={{ maxLength: 1, style: { textAlign: "center" } }}
              value={digit}
              onChange={(e) => handleChange(e, index)}
              sx={{
                width: 45,
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                  '&.Mui-focused': {
                    boxShadow: "0 0 0 2px rgba(46, 192, 203, 0.3)"
                  }
                }
              }}
            />
          ))}
        </Box>

        <Typography variant="body2" color="text.secondary" mb={4}>
          {timer > 0 ? (
            <>Resend OTP in <strong>{timer}s</strong></>
          ) : (
            <Button onClick={resendOTP} size="small" sx={{ color: "#2EC0CB" }}>
              Resend OTP
            </Button>
          )}
        </Typography>

        <Button
          fullWidth
          variant="contained"
          onClick={handleSubmit}
          sx={{
            background: "linear-gradient(to right, #2EC0CB, #23A3AD)",
            fontWeight: "bold",
            py: 1.5,
            fontSize: "1rem",
            borderRadius: "12px",
            textTransform: "none",
            ":hover": {
              background: "linear-gradient(to right, #23A3AD, #2EC0CB)",
            },
          }}
        >
          Verify OTP
        </Button>
      </div>
    </AuthLayout>
  );
};

export default VerifyOTP;
