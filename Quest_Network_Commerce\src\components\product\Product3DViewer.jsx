import React, { useRef, useEffect, useState } from 'react';
import {
  Box,
  Paper,
  IconButton,
  Typography,
  Stack,
  Slider,
  Button,
  Dialog,
  DialogContent,
  DialogActions,
  Tooltip,
  Chip,
  CircularProgress
} from '@mui/material';
import ThreeDRotationIcon from '@mui/icons-material/ThreeDRotation';
import ViewInArIcon from '@mui/icons-material/ViewInAr';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import FullscreenExitIcon from '@mui/icons-material/FullscreenExit';
import RotateLeftIcon from '@mui/icons-material/RotateLeft';
import RotateRightIcon from '@mui/icons-material/RotateRight';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import CloseIcon from '@mui/icons-material/Close';

// Mock 3D viewer implementation (would use Three.js in production)
function Product3DViewer({ product, open, onClose }) {
  const viewerRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [rotation, setRotation] = useState({ x: 0, y: 0, z: 0 });
  const [zoom, setZoom] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [autoRotate, setAutoRotate] = useState(false);
  const [showWireframe, setShowWireframe] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState('default');

  // Mock materials for demonstration
  const materials = [
    { id: 'default', name: 'Default', color: '#666666' },
    { id: 'metallic', name: 'Metallic', color: '#C0C0C0' },
    { id: 'ceramic', name: 'Ceramic', color: '#F5F5DC' },
    { id: 'carbon', name: 'Carbon Fiber', color: '#2C2C2C' }
  ];

  useEffect(() => {
    if (open) {
      // Simulate 3D model loading
      setIsLoading(true);
      const timer = setTimeout(() => {
        setIsLoading(false);
        initializeViewer();
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [open]);

  useEffect(() => {
    let animationFrame;
    if (autoRotate && !isLoading) {
      const animate = () => {
        setRotation(prev => ({
          ...prev,
          y: (prev.y + 1) % 360
        }));
        animationFrame = requestAnimationFrame(animate);
      };
      animationFrame = requestAnimationFrame(animate);
    }
    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [autoRotate, isLoading]);

  const initializeViewer = () => {
    // In a real implementation, this would initialize Three.js scene
    console.log('Initializing 3D viewer for product:', product.name?.en);
  };

  const handleRotationChange = (axis, value) => {
    setRotation(prev => ({
      ...prev,
      [axis]: value
    }));
  };

  const handleZoomChange = (_, newValue) => {
    setZoom(newValue);
  };

  const resetView = () => {
    setRotation({ x: 0, y: 0, z: 0 });
    setZoom(1);
    setAutoRotate(false);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const startARView = () => {
    // AR implementation would go here
    alert('AR view would be implemented using WebXR or AR.js');
  };

  const renderViewer = () => {
    if (isLoading) {
      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            minHeight: 400
          }}
        >
          <CircularProgress size={60} sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Loading 3D Model...
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Preparing interactive 3D view
          </Typography>
        </Box>
      );
    }

    return (
      <Box
        ref={viewerRef}
        sx={{
          width: '100%',
          height: '100%',
          minHeight: 400,
          position: 'relative',
          background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
          borderRadius: 2,
          overflow: 'hidden',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        {/* Mock 3D Object */}
        <Box
          sx={{
            width: 200,
            height: 200,
            background: materials.find(m => m.id === selectedMaterial)?.color || '#666666',
            borderRadius: showWireframe ? 0 : 2,
            border: showWireframe ? '2px solid #333' : 'none',
            transform: `
              rotateX(${rotation.x}deg) 
              rotateY(${rotation.y}deg) 
              rotateZ(${rotation.z}deg) 
              scale(${zoom})
            `,
            transition: autoRotate ? 'none' : 'transform 0.3s ease',
            boxShadow: showWireframe ? 'none' : '0 10px 30px rgba(0,0,0,0.3)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: '20%',
              left: '20%',
              right: '20%',
              bottom: '20%',
              background: 'rgba(255,255,255,0.2)',
              borderRadius: showWireframe ? 0 : 1,
              transform: 'translateZ(10px)'
            }
          }}
        >
          <Typography variant="h6" color="white" sx={{ zIndex: 1 }}>
            3D
          </Typography>
        </Box>

        {/* 3D Controls Overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            display: 'flex',
            flexDirection: 'column',
            gap: 1
          }}
        >
          <Tooltip title="Auto Rotate">
            <IconButton
              onClick={() => setAutoRotate(!autoRotate)}
              sx={{
                bgcolor: autoRotate ? 'primary.main' : 'rgba(255,255,255,0.9)',
                color: autoRotate ? 'white' : 'inherit',
                '&:hover': { bgcolor: autoRotate ? 'primary.dark' : 'rgba(255,255,255,1)' }
              }}
            >
              <ThreeDRotationIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Wireframe Mode">
            <IconButton
              onClick={() => setShowWireframe(!showWireframe)}
              sx={{
                bgcolor: showWireframe ? 'secondary.main' : 'rgba(255,255,255,0.9)',
                color: showWireframe ? 'white' : 'inherit',
                '&:hover': { bgcolor: showWireframe ? 'secondary.dark' : 'rgba(255,255,255,1)' }
              }}
            >
              <ThreeDRotationIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Reset View">
            <IconButton
              onClick={resetView}
              sx={{ bgcolor: 'rgba(255,255,255,0.9)', '&:hover': { bgcolor: 'rgba(255,255,255,1)' } }}
            >
              <RestartAltIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Fullscreen">
            <IconButton
              onClick={toggleFullscreen}
              sx={{ bgcolor: 'rgba(255,255,255,0.9)', '&:hover': { bgcolor: 'rgba(255,255,255,1)' } }}
            >
              {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
            </IconButton>
          </Tooltip>
        </Box>

        {/* Material Selector */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 16,
            left: 16,
            display: 'flex',
            gap: 1,
            flexWrap: 'wrap'
          }}
        >
          {materials.map((material) => (
            <Chip
              key={material.id}
              label={material.name}
              onClick={() => setSelectedMaterial(material.id)}
              variant={selectedMaterial === material.id ? 'filled' : 'outlined'}
              color={selectedMaterial === material.id ? 'primary' : 'default'}
              size="small"
              sx={{
                bgcolor: selectedMaterial === material.id ? 'primary.main' : 'rgba(255,255,255,0.9)',
                '&:hover': { bgcolor: selectedMaterial === material.id ? 'primary.dark' : 'rgba(255,255,255,1)' }
              }}
            />
          ))}
        </Box>
      </Box>
    );
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      fullScreen={isFullscreen}
      sx={{
        '& .MuiDialog-paper': {
          height: isFullscreen ? '100vh' : '80vh'
        }
      }}
    >
      <DialogContent sx={{ p: 0, display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Box>
              <Typography variant="h6">3D Product Viewer</Typography>
              <Typography variant="body2" color="text.secondary">
                {product.name?.en}
              </Typography>
            </Box>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Stack>
        </Box>

        {/* 3D Viewer */}
        <Box sx={{ flex: 1, p: 2 }}>
          {renderViewer()}
        </Box>

        {/* Controls Panel */}
        {!isLoading && (
          <Paper elevation={1} sx={{ m: 2, p: 2 }}>
            <Stack spacing={3}>
              {/* Rotation Controls */}
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Rotation Controls
                </Typography>
                <Stack direction="row" spacing={2} alignItems="center">
                  <IconButton onClick={() => handleRotationChange('y', rotation.y - 15)}>
                    <RotateLeftIcon />
                  </IconButton>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="caption">Y-Axis: {rotation.y}°</Typography>
                    <Slider
                      value={rotation.y}
                      onChange={(_, value) => handleRotationChange('y', value)}
                      min={0}
                      max={360}
                      size="small"
                    />
                  </Box>
                  <IconButton onClick={() => handleRotationChange('y', rotation.y + 15)}>
                    <RotateRightIcon />
                  </IconButton>
                </Stack>
              </Box>

              {/* Zoom Control */}
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Zoom: {Math.round(zoom * 100)}%
                </Typography>
                <Stack direction="row" spacing={2} alignItems="center">
                  <IconButton onClick={() => setZoom(Math.max(0.5, zoom - 0.1))}>
                    <ZoomOutIcon />
                  </IconButton>
                  <Slider
                    value={zoom}
                    onChange={handleZoomChange}
                    min={0.5}
                    max={3}
                    step={0.1}
                    size="small"
                    sx={{ flex: 1 }}
                  />
                  <IconButton onClick={() => setZoom(Math.min(3, zoom + 0.1))}>
                    <ZoomInIcon />
                  </IconButton>
                </Stack>
              </Box>
            </Stack>
          </Paper>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose} variant="outlined">
          Close
        </Button>
        <Button
          onClick={startARView}
          variant="contained"
          startIcon={<ViewInArIcon />}
          disabled={isLoading}
        >
          View in AR
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default Product3DViewer;
