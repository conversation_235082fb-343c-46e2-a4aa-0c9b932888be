import { useState, useEffect } from "react";
import { TextField, InputAdornment, MenuItem, Select, Box, Typography } from "@mui/material";
import { parsePhoneNumberFromString } from "libphonenumber-js";

// Country data with flags and calling codes
const COUNTRIES = [
  { code: 'US', name: 'United States', callingCode: '1', flag: '🇺🇸' },
  { code: 'IN', name: 'India', callingCode: '91', flag: '🇮🇳' },
  { code: 'GB', name: 'United Kingdom', callingCode: '44', flag: '🇬🇧' },
  { code: 'CA', name: 'Canada', callingCode: '1', flag: '🇨🇦' },
  { code: 'AU', name: 'Australia', callingCode: '61', flag: '🇦🇺' },
  { code: 'DE', name: 'Germany', callingCode: '49', flag: '🇩🇪' },
  { code: 'FR', name: 'France', callingCode: '33', flag: '🇫🇷' },
  { code: 'JP', name: 'Japan', callingCode: '81', flag: '🇯🇵' },
  { code: 'CN', name: 'China', callingCode: '86', flag: '🇨🇳' },
  { code: 'BR', name: 'Brazil', callingCode: '55', flag: '🇧🇷' },
];

const SmartPhoneInput = ({
  value = '',
  onChange,
  error,
  helperText,
  label = "Phone Number",
  placeholder = "Enter phone number",
  name,
  showCountrySelector: externalShowCountrySelector,
  ...props
}) => {
  const [selectedCountry, setSelectedCountry] = useState('IN');
  const [showCountrySelector, setShowCountrySelector] = useState(false);
  const [detectedCountry, setDetectedCountry] = useState(null);
  const [internalValue, setInternalValue] = useState(value || '');

  // Sync with external value changes
  useEffect(() => {
    if (value !== internalValue) {
      const newValue = value || '';
      // Check if the new value should show country selector
      const isPhone = isPhoneNumberLike(newValue);
      setShowCountrySelector(isPhone);

      if (isPhone) {
        // Try to format for display (national format to avoid duplication)
        try {
          const parsed = parsePhoneNumberFromString(newValue);
          if (parsed && parsed.isValid()) {
            setInternalValue(parsed.formatNational());
            if (parsed.country) {
              setSelectedCountry(parsed.country);
            }
            return;
          }
        } catch (e) {
          // Continue with original value
        }
      }

      setInternalValue(newValue);
    }
  }, [value, internalValue]);

  // Detect if input looks like a phone number
  const isPhoneNumberLike = (input) => {
    if (!input) return false;
    // Contains digits and doesn't contain @ (email indicator)
    return /\d/.test(input) && !/@/.test(input);
  };

  // Detect country from phone number
  const detectCountry = (phoneNumber) => {
    if (!phoneNumber) return null;
    
    try {
      const parsed = parsePhoneNumberFromString(phoneNumber);
      if (parsed && parsed.country) {
        return parsed.country;
      }
    } catch (e) {
      // Continue with manual detection
    }
    
    // Manual detection by calling code
    const digits = phoneNumber.replace(/\D/g, '');
    if (digits.startsWith('1')) return 'US';
    if (digits.startsWith('91')) return 'IN';
    if (digits.startsWith('44')) return 'GB';
    if (digits.startsWith('61')) return 'AU';
    if (digits.startsWith('49')) return 'DE';
    if (digits.startsWith('33')) return 'FR';
    if (digits.startsWith('81')) return 'JP';
    if (digits.startsWith('86')) return 'CN';
    if (digits.startsWith('55')) return 'BR';
    
    return null;
  };

  const handleInputChange = (e) => {
    const newValue = e.target.value;
    setInternalValue(newValue);

    // Check if it looks like a phone number
    const isPhone = isPhoneNumberLike(newValue);
    setShowCountrySelector(isPhone);

    if (isPhone) {
      // Try to detect country
      const country = detectCountry(newValue);
      if (country && country !== selectedCountry) {
        setSelectedCountry(country);
        setDetectedCountry(country);
      }

      // Try to format the number only if it's a valid phone number
      try {
        const parsed = parsePhoneNumberFromString(newValue, selectedCountry);
        if (parsed && parsed.isValid()) {
          // Use national format to avoid duplication with country selector
          const formatted = parsed.formatNational();
          setInternalValue(formatted);
          onChange && onChange({ target: { value: parsed.formatInternational(), name } });
          return;
        }
      } catch (e) {
        // Continue with original value
      }
    }

    // Pass through the original value
    onChange && onChange({ target: { value: newValue, name } });
  };

  const handleCountryChange = (event) => {
    const newCountry = event.target.value;
    setSelectedCountry(newCountry);

    // Reformat current value with new country
    if (internalValue && isPhoneNumberLike(internalValue)) {
      try {
        const parsed = parsePhoneNumberFromString(internalValue, newCountry);
        if (parsed) {
          // Use national format for display, international for value
          const formatted = parsed.formatNational();
          setInternalValue(formatted);
          onChange && onChange({ target: { value: parsed.formatInternational(), name } });
        }
      } catch (e) {
        // Keep original value
      }
    }
  };

  const currentCountry = COUNTRIES.find(c => c.code === selectedCountry) || COUNTRIES[1]; // Default to India

  return (
    <TextField
      fullWidth
      label={label}
      placeholder={placeholder}
      value={internalValue}
      onChange={handleInputChange}
      error={!!error}
      helperText={helperText}
      name={name}
      InputProps={{
        startAdornment: showCountrySelector ? (
          <InputAdornment position="start">
            <Select
              value={selectedCountry}
              onChange={handleCountryChange}
              variant="standard"
              disableUnderline
              sx={{
                minWidth: 70,
                '& .MuiSelect-select': {
                  display: 'flex',
                  alignItems: 'center',
                  paddingRight: '20px !important',
                },
              }}
              renderValue={(value) => {
                const country = COUNTRIES.find(c => c.code === value);
                return (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <span style={{ fontSize: '14px' }}>{country?.flag}</span>
                    <Typography variant="caption">+{country?.callingCode}</Typography>
                  </Box>
                );
              }}
            >
              {COUNTRIES.map((country) => (
                <MenuItem key={country.code} value={country.code}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <span style={{ fontSize: '16px' }}>{country.flag}</span>
                    <Typography variant="body2">
                      {country.name} (+{country.callingCode})
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </InputAdornment>
        ) : null,
        sx: {
          borderRadius: "12px",
          backgroundColor: "#f9f9f9",
          fontSize: "0.9rem",
          fontWeight: 500,
          height: "56px",
          fontFamily: "'Roobert', sans-serif",
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: "#2EC0CB",
          },
          '&.Mui-focused': {
            backgroundColor: "#ffffff",
            boxShadow: "0 0 0 2px rgba(46, 192, 203, 0.2)",
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: "#2EC0CB",
            borderWidth: "2px",
          },
          '&.Mui-error .MuiOutlinedInput-notchedOutline': {
            borderColor: "#f44336",
          },
          '&.Mui-error:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: "#f44336",
          },
        }
      }}
      InputLabelProps={{
        // Dynamic shrink based on focus or value - same as StyledTextField
        shrink: undefined, // Let Material-UI handle this automatically
        sx: {
          color: "#666",
          fontFamily: "'HCLTechRoobert', 'Roobert', Roboto, 'Helvetica Neue', Arial, sans-serif",
          fontSize: "1rem",
          fontWeight: 500,
          transformOrigin: "top left",
          transition: "all 300ms cubic-bezier(0.4, 0, 0.2, 1)",
          // When not focused and no value (placeholder state)
          transform: "translate(14px, 16px) scale(1)",
          // When focused or has value (label state)
          "&.MuiInputLabel-shrink": {
            color: "#2EC0CB",
            transform: "translate(14px, -9px) scale(0.75)",
            fontWeight: 600,
            backgroundColor: "#ffffff",
            padding: "0 8px",
            marginLeft: "-4px",
          },
          "&.Mui-focused": {
            color: "#2EC0CB",
            fontWeight: 600,
          },
          "&.Mui-error": {
            color: "#f44336"
          },
          "&.Mui-error.MuiInputLabel-shrink": {
            color: "#f44336",
            backgroundColor: "#ffffff",
            padding: "0 8px",
            marginLeft: "-4px",
          }
        }
      }}
      FormHelperTextProps={{
        sx: {
          fontSize: "0.75rem",
        }
      }}
      {...props}
    />
  );
};

export default SmartPhoneInput;
