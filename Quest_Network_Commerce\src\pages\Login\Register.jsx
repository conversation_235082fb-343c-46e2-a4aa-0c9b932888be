import { useState } from "react";
import { useForm } from "react-hook-form";
import { Link, useNavigate } from "react-router-dom";
import {
  Button,
  Typography,
  Box,
  FormControlLabel,
  Checkbox,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  FormControl,
  Select,
  MenuItem,
  Chip,
  TextField
} from "@mui/material";
import StyledTextField from "../../components/Login/StyledTextField";
import AuthLayout from "../../components/Login/AuthLayout";
import SmartPhoneInput from "../../components/Login/SmartPhoneInput";
import CustomSnackbar from "../../components/Login/CustomSnackbar";
import SocialAuthDialog from "../../components/Login/SocialAuthDialog";

import {
  Google,
  Facebook,
  Apple,
  Twitter,
  Microsoft,
  Close,
  CloudUpload,
  AttachFile,
  Delete
} from "@mui/icons-material";



const Register = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [verificationSent, setVerificationSent] = useState(false);
  const [verificationCode, setVerificationCode] = useState("");
  const [isVerified, setIsVerified] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "info",
    title: null
  });
  const [socialDialog, setSocialDialog] = useState({
    open: false,
    provider: '',
    icon: null
  });
  const [contactUsOpen, setContactUsOpen] = useState(false);
  const [contactForm, setContactForm] = useState({
    name: "",
    email: "",
    phone: "",
    countryCode: "+1",
    subject: [],
    description: "",
    attachments: []
  });
  const [showVerificationAlert, setShowVerificationAlert] = useState(false);



  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: {
      fullName: "",
      username: "",
      contact: "",
      password: "",
      confirmPassword: "",
    },
  });

  const navigate = useNavigate();

  const handleContactChange = (e) => {
    const value = e.target.value;
    setValue("contact", value);

    // Check if it's a valid email or phone number (basic validation)
    const isEmail = /\S+@\S+\.\S+/.test(value);
    const isPhone = /^\+?[\d\s\-\(\)]{10,}$/.test(value);

    if ((isEmail || isPhone) && value.length > 5 && !verificationSent) {
      setVerificationSent(true);
      setShowVerificationAlert(true);
      setTimeout(() => setShowVerificationAlert(false), 3000);
    }
  };

  const showSnackbar = (message, severity = "info", title = null) => {
    setSnackbar({
      open: true,
      message,
      severity,
      title
    });
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const getSocialIcon = (iconName) => {
    const iconProps = { sx: { fontSize: 20, color: '#666' } };
    switch (iconName) {
      case 'google':
        return <Google {...iconProps} sx={{ ...iconProps.sx, color: '#DB4437' }} />;
      case 'facebook':
        return <Facebook {...iconProps} sx={{ ...iconProps.sx, color: '#4267B2' }} />;
      case 'apple':
        return <Apple {...iconProps} sx={{ ...iconProps.sx, color: '#000' }} />;
      case 'twitter':
        return <Twitter {...iconProps} sx={{ ...iconProps.sx, color: '#1DA1F2' }} />;
      case 'microsoft':
        return <Microsoft {...iconProps} sx={{ ...iconProps.sx, color: '#00A1F1' }} />;
      default:
        return null;
    }
  };

  const handleSocialAuth = (provider, icon) => {
    setSocialDialog({
      open: true,
      provider,
      icon
    });
  };

  const handleCloseSocialDialog = () => {
    setSocialDialog({
      open: false,
      provider: '',
      icon: null
    });
  };

  const handleVerifyCode = () => {
    if (verificationCode === "123456") {
      setIsVerified(true);
      showSnackbar("Verification successful! You can now set your password.", "success", "Email/Phone Verified");
    } else {
      showSnackbar("Invalid verification code. Please try again.", "error", "Verification Failed");
    }
  };

  const onSubmit = (data) => {
    if (!isVerified) {
      showSnackbar("Please verify your email/phone number first!", "warning", "Verification Required");
      return;
    }

    if (data.password !== data.confirmPassword) {
      showSnackbar("Passwords do not match! Please check and try again.", "error", "Password Mismatch");
      return;
    }

    if (!termsAccepted) {
      showSnackbar("Please accept the Terms and Conditions to continue.", "warning", "Terms Required");
      return;
    }

    console.log("Registration data:", data);
    setShowSuccess(true);
    showSnackbar("Registration successful! Redirecting to login page...", "success", "Welcome!");
    setTimeout(() => {
      navigate("/login");
    }, 2000);
  };

  return (
    <AuthLayout title="Create Account" subtitle="Join us today and get started">
      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
        <div>
          <form onSubmit={handleSubmit(onSubmit)} style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <StyledTextField
              label="Full Name"
              value={watch("fullName")}
              onChange={(e) => setValue("fullName", e.target.value)}
              error={!!errors.fullName}
              helperText={errors.fullName?.message}
              validation={{
                required: true,
                minLength: 2
              }}
              {...register("fullName", {
                required: "Full name is required",
                minLength: {
                  value: 2,
                  message: "Full name must be at least 2 characters long"
                },
                maxLength: {
                  value: 50,
                  message: "Full name must not exceed 50 characters"
                },
                pattern: {
                  value: /^[a-zA-Z\s]+$/,
                  message: "Full name can only contain letters and spaces"
                }
              })}
            />

            <StyledTextField
              label="Username"
              value={watch("username")}
              onChange={(e) => setValue("username", e.target.value)}
              error={!!errors.username}
              helperText={errors.username?.message}
              {...register("username", {
                required: "Username is required",
                minLength: {
                  value: 3,
                  message: "Username must be at least 3 characters long"
                },
                maxLength: {
                  value: 20,
                  message: "Username must not exceed 20 characters"
                },
                pattern: {
                  value: /^[a-zA-Z0-9_]+$/,
                  message: "Username can only contain letters, numbers, and underscores"
                }
              })}
            />

            <SmartPhoneInput
              label="Email or Phone"
              placeholder="Enter email or phone number"
              value={watch("contact")}
              onChange={handleContactChange}
              error={!!errors.contact}
              helperText={errors.contact?.message}
              name="contact"
            />

            {/* Verification Code Section */}
            {verificationSent && (
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start' }}>
                <StyledTextField
                  label="Verification Code"
                  placeholder="Enter 6-digit code"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                  inputProps={{ maxLength: 6 }}
                  sx={{ flex: 1 }}
                  disabled={isVerified}
                  helperText={isVerified ? "✓ Verified" : "Check your email/phone for the code"}
                />
                <Button
                  variant="contained"
                  onClick={handleVerifyCode}
                  disabled={verificationCode.length !== 6 || isVerified}
                  sx={{
                    background: isVerified ? "#4CAF50" : "linear-gradient(to right, #2EC0CB, #23A3AD)",
                    fontWeight: "bold",
                    px: 2,
                    py: 1,
                    fontSize: "0.8rem",
                    borderRadius: "12px",
                    textTransform: "none",
                    minWidth: "80px",
                    height: "48px",
                    ":hover": {
                      background: isVerified ? "#45a049" : "linear-gradient(to right, #23A3AD, #2EC0CB)",
                    },
                  }}
                >
                  {isVerified ? "✓ Verified" : "Verify"}
                </Button>
              </Box>
            )}

            <StyledTextField
              label="Password"
              type="password"
              showPasswordToggle={true}
              value={watch("password")}
              onChange={(e) => setValue("password", e.target.value)}
              error={!!errors.password}
              helperText={!isVerified ? "Please verify your email/phone first" : errors.password?.message}
              disabled={!isVerified}
              validation={{
                required: true,
                password: true
              }}
              {...register("password", {
                required: "Password is required",
                minLength: {
                  value: 8,
                  message: "Password must be at least 8 characters long"
                },
                pattern: {
                  value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
                  message: "Password must contain at least 1 uppercase, 1 lowercase, 1 number, and 1 special character"
                }
              })}
            />

            <StyledTextField
              label="Confirm Password"
              type="password"
              showPasswordToggle={true}
              value={watch("confirmPassword")}
              onChange={(e) => setValue("confirmPassword", e.target.value)}
              error={!!errors.confirmPassword}
              helperText={!isVerified ? "Please verify your email/phone first" : errors.confirmPassword?.message}
              disabled={!isVerified}
              validation={{
                required: true
              }}
              {...register("confirmPassword", {
                required: "Please confirm your password",
                validate: (value) => {
                  if (value !== watch("password")) {
                    return "Passwords do not match";
                  }
                  return true;
                }
              })}
            />

            <FormControlLabel
              control={
                <Checkbox
                  sx={{ color: "#2EC0CB" }}
                  checked={termsAccepted}
                  onChange={(e) => setTermsAccepted(e.target.checked)}
                />
              }
              label={
                <Typography sx={{ fontSize: "0.9rem", color: "#2EC0CB" }}>
                  I agree to the Terms and Conditions
                </Typography>
              }
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{
                background: "linear-gradient(to right, #2EC0CB, #23A3AD)",
                fontWeight: "bold",
                py: 1.2,
                fontSize: "0.9rem",
                borderRadius: "12px",
                textTransform: "none",
                height: "48px",
              }}
            >
              REGISTER
            </Button>
          </form>

          {/* OR Divider */}
          <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>
            <Box sx={{ flex: 1, height: '1px', backgroundColor: '#E0E0E0' }} />
            <Typography 
              sx={{ 
                mx: 3, 
                color: '#666', 
                fontSize: '0.9rem', 
                fontWeight: 'bold',
                backgroundColor: 'white',
                px: 2
              }}
            >
              OR
            </Typography>
            <Box sx={{ flex: 1, height: '1px', backgroundColor: '#E0E0E0' }} />
          </Box>

          {/* Social Media Registration */}
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <Typography
              sx={{
                fontSize: '0.75rem',
                color: '#666',
                mb: 1,
                fontWeight: '500',
                textAlign: 'center'
              }}
            >
              Register using
            </Typography>
            <Box sx={{ display: 'flex', gap: 1.5, justifyContent: 'center' }}>
              {["google", "facebook", "apple", "twitter", "microsoft"].map((icon) => (
                <Button
                  key={icon}
                  onClick={() => handleSocialAuth(
                    icon.charAt(0).toUpperCase() + icon.slice(1),
                    getSocialIcon(icon)
                  )}
                  sx={{
                    padding: 1,
                    borderRadius: "50%",
                    border: "2px solid #2EC0CB",
                    minWidth: 'auto',
                    width: 40,
                    height: 40,
                    "&:hover": {
                      backgroundColor: "#E0F7F9",
                      borderColor: "#23A3AD"
                    }
                  }}
                >
                  {getSocialIcon(icon)}
                </Button>
              ))}
            </Box>
          </Box>

          <Typography align="center" sx={{ mt: 2, fontSize: "0.85rem" }}>
            Already have an account?{" "}
            <Link to="/login" style={{ color: "#2EC0CB", fontWeight: 600 }}>
              Login
            </Link>
          </Typography>

          <Typography align="center" sx={{ mt: 1, fontSize: "0.8rem" }}>
            Having issues?{" "}
            <span
              onClick={() => setContactUsOpen(true)}
              style={{
                color: "#2EC0CB",
                fontWeight: 600,
                textDecoration: "none",
                cursor: "pointer"
              }}
            >
              Contact Us
            </span>
          </Typography>



          <Snackbar
            open={showSuccess}
            autoHideDuration={1500}
            anchorOrigin={{ vertical: "top", horizontal: "center" }}
          >
            <Alert severity="success" sx={{ width: "100%" }}>
              Registration successful! You can now login.
            </Alert>
          </Snackbar>

          <Snackbar
            open={showVerificationAlert}
            autoHideDuration={3000}
            anchorOrigin={{ vertical: "top", horizontal: "center" }}
          >
            <Alert severity="info" sx={{ width: "100%" }}>
              Verification code sent! Check your email/phone.
            </Alert>
          </Snackbar>

          <CustomSnackbar
            open={snackbar.open}
            onClose={handleCloseSnackbar}
            message={snackbar.message}
            severity={snackbar.severity}
            title={snackbar.title}
          />

          <SocialAuthDialog
            open={socialDialog.open}
            onClose={handleCloseSocialDialog}
            provider={socialDialog.provider}
            icon={socialDialog.icon}
          />

        </div>
      </div>

        {/* Contact Us Modal */}
        <Dialog
          open={contactUsOpen}
          onClose={() => setContactUsOpen(false)}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: "20px",
              maxHeight: "95vh",
              background: "linear-gradient(135deg, #ffffff 0%, #f8fdfe 100%)",
              boxShadow: "0 20px 60px rgba(46, 192, 203, 0.15)",
              overflow: "hidden"
            }
          }}
        >
          <DialogTitle sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            background: "linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)",
            color: "white",
            fontFamily: "'HCLTechRoobert', sans-serif",
            padding: "24px 32px",
            borderBottom: "none"
          }}>
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 700, mb: 0.5 }}>
                Contact Support
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9, fontWeight: 400 }}>
                We're here to help you with any login or account issues
              </Typography>
            </Box>
            <IconButton
              onClick={() => setContactUsOpen(false)}
              sx={{
                color: "white",
                backgroundColor: "rgba(255,255,255,0.1)",
                "&:hover": {
                  backgroundColor: "rgba(255,255,255,0.2)"
                }
              }}
            >
              <Close />
            </IconButton>
          </DialogTitle>

          <DialogContent sx={{ p: "32px", pt: "24px", backgroundColor: "transparent" }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 2 }}>
              <StyledTextField
                label="Full Name (Optional)"
                value={contactForm.name}
                onChange={(e) => setContactForm(prev => ({ ...prev, name: e.target.value }))}
                fullWidth
                placeholder="Enter your full name"
                validation={{
                  maxLength: 50
                }}
              />

              <StyledTextField
                label="Email Address *"
                type="email"
                value={contactForm.email}
                onChange={(e) => setContactForm(prev => ({ ...prev, email: e.target.value }))}
                fullWidth
                required
                placeholder="Enter your email address"
                validation={{
                  required: true,
                  email: true
                }}
              />

              <StyledTextField
                label="Phone Number *"
                value={contactForm.phone}
                onChange={(e) => setContactForm(prev => ({ ...prev, phone: e.target.value }))}
                fullWidth
                required
                placeholder="Enter your phone number"
                validation={{
                  required: true,
                  phone: true
                }}
              />

              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 600, color: '#333' }}>
                    Subject *
                  </Typography>
                  {contactForm.subject.length > 0 && (
                    <Button
                      size="small"
                      onClick={() => setContactForm(prev => ({ ...prev, subject: [] }))}
                      sx={{
                        color: '#2EC0CB',
                        fontSize: '0.75rem',
                        textTransform: 'none',
                        minWidth: 'auto',
                        padding: '2px 8px',
                        '&:hover': {
                          backgroundColor: '#E0F7F9'
                        }
                      }}
                    >
                      Clear All
                    </Button>
                  )}
                </Box>
                <FormControl fullWidth required>
                  <Select
                    multiple
                    value={contactForm.subject}
                    onChange={(e) => setContactForm(prev => ({ ...prev, subject: e.target.value }))}
                    displayEmpty
                    onClose={(e) => e.stopPropagation()}
                    renderValue={(selected) => {
                      if (selected.length === 0) {
                        return <Typography sx={{ color: '#999' }}>Select issue categories</Typography>;
                      }
                      return (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip
                              key={value}
                              label={value}
                              size="small"
                              onDelete={(e) => {
                                e.stopPropagation();
                                setContactForm(prev => ({
                                  ...prev,
                                  subject: prev.subject.filter(s => s !== value)
                                }));
                              }}
                              onMouseDown={(e) => e.stopPropagation()}
                              sx={{
                                backgroundColor: '#E0F7F9',
                                color: '#2EC0CB',
                                fontWeight: 500,
                                '& .MuiChip-deleteIcon': {
                                  color: '#2EC0CB',
                                  '&:hover': {
                                    color: '#23A3AD'
                                  }
                                }
                              }}
                            />
                          ))}
                        </Box>
                      );
                    }}
                    sx={{
                      borderRadius: '12px',
                      backgroundColor: '#ffffff',
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#E0E7FF',
                        borderWidth: '2px',
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#2EC0CB',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#2EC0CB',
                        borderWidth: '2px',
                      },
                      '& .MuiSelect-select': {
                        padding: '16px 14px',
                        minHeight: '24px',
                      }
                    }}
                  >
                    {[
                      "Login Issues",
                      "Password Reset Problems",
                      "Account Access Denied",
                      "Two-Factor Authentication Issues",
                      "Email Verification Problems",
                      "Account Locked/Suspended",
                      "Username/Email Not Recognized",
                      "Social Media Login Issues",
                      "Browser Compatibility Issues",
                      "Mobile App Login Problems",
                      "Other"
                    ].map((option) => (
                      <MenuItem
                        key={option}
                        value={option}
                        sx={{
                          '&:hover': {
                            backgroundColor: '#E0F7F9'
                          },
                          '&.Mui-selected': {
                            backgroundColor: '#E0F7F9',
                            '&:hover': {
                              backgroundColor: '#B8F0F3'
                            }
                          }
                        }}
                      >
                        {option}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>

              <TextField
                label={contactForm.subject.includes("Other") ? "Description *" : "Description (Optional)"}
                multiline
                rows={4}
                value={contactForm.description}
                onChange={(e) => setContactForm(prev => ({ ...prev, description: e.target.value }))}
                fullWidth
                required={contactForm.subject.includes("Other")}
                placeholder="Please describe your issue in detail..."
                variant="outlined"
                sx={{
                  borderRadius: '12px',
                  backgroundColor: '#ffffff',
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '12px',
                    '& fieldset': {
                      borderColor: '#E0E7FF',
                      borderWidth: '2px',
                    },
                    '&:hover fieldset': {
                      borderColor: '#2EC0CB',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#2EC0CB',
                      borderWidth: '2px',
                    },
                    '& textarea': {
                      color: '#333',
                      fontSize: '16px',
                      lineHeight: '1.5',
                      '&::placeholder': {
                        color: '#999',
                        opacity: 1,
                      }
                    }
                  },
                  '& .MuiInputLabel-root': {
                    color: '#666',
                    fontSize: '16px',
                    '&.Mui-focused': {
                      color: '#2EC0CB',
                    },
                  },
                }}
              />

              {/* File Attachments */}
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 600, color: '#333', mb: 2 }}>
                  Attachments (Optional)
                </Typography>
                <Box
                  sx={{
                    border: '2px dashed #E0E7FF',
                    borderRadius: '12px',
                    padding: '24px',
                    textAlign: 'center',
                    backgroundColor: '#fafbff',
                    transition: 'all 0.3s ease',
                    cursor: 'pointer',
                    '&:hover': {
                      borderColor: '#2EC0CB',
                      backgroundColor: '#f0fdfe'
                    }
                  }}
                  onClick={() => document.getElementById('file-upload').click()}
                >
                  <input
                    id="file-upload"
                    type="file"
                    multiple
                    style={{ display: 'none' }}
                    onChange={(e) => {
                      const files = Array.from(e.target.files);
                      setContactForm(prev => ({
                        ...prev,
                        attachments: [...prev.attachments, ...files]
                      }));
                    }}
                  />
                  <CloudUpload sx={{ fontSize: 48, color: '#2EC0CB', mb: 1 }} />
                  <Typography variant="body1" sx={{ fontWeight: 600, color: '#333', mb: 0.5 }}>
                    Drop files here or click to browse
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    Supported formats: PDF, DOC, DOCX, JPG, PNG (Max 10MB each)
                  </Typography>
                </Box>

                {/* Display uploaded files */}
                {contactForm.attachments.length > 0 && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2" sx={{ fontWeight: 600, color: '#333', mb: 1 }}>
                      Uploaded Files ({contactForm.attachments.length})
                    </Typography>
                    {contactForm.attachments.map((file, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          padding: '8px 12px',
                          backgroundColor: '#E0F7F9',
                          borderRadius: '8px',
                          mb: 1
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <AttachFile sx={{ color: '#2EC0CB', mr: 1, fontSize: 20 }} />
                          <Typography variant="body2" sx={{ color: '#333' }}>
                            {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                          </Typography>
                        </Box>
                        <IconButton
                          size="small"
                          onClick={() => {
                            setContactForm(prev => ({
                              ...prev,
                              attachments: prev.attachments.filter((_, i) => i !== index)
                            }));
                          }}
                          sx={{ color: '#666' }}
                        >
                          <Delete fontSize="small" />
                        </IconButton>
                      </Box>
                    ))}
                  </Box>
                )}
              </Box>
            </Box>
          </DialogContent>

          <DialogActions sx={{
            p: "24px 32px",
            backgroundColor: "#f8fdfe",
            borderTop: "1px solid #E0F7F9",
            gap: 2
          }}>
            <Button
              onClick={() => setContactUsOpen(false)}
              variant="outlined"
              sx={{
                color: '#666',
                borderColor: '#ddd',
                fontWeight: 600,
                borderRadius: '12px',
                padding: '12px 24px',
                textTransform: 'none',
                '&:hover': {
                  backgroundColor: '#f5f5f5',
                  borderColor: '#ccc'
                }
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (!contactForm.email || !contactForm.phone || contactForm.subject.length === 0) {
                  alert("Please fill in all required fields (Email, Phone Number, Subject)");
                  return;
                }
                if (contactForm.subject.includes("Other") && !contactForm.description.trim()) {
                  alert("Description is required when 'Other' is selected");
                  return;
                }
                alert("✅ Ticket Raised Successfully! We'll get back to you soon.");
                setContactUsOpen(false);
                setContactForm({
                  name: "",
                  email: "",
                  phone: "",
                  countryCode: "+1",
                  subject: [],
                  description: "",
                  attachments: []
                });
              }}
              variant="contained"
              sx={{
                background: "linear-gradient(135deg, #2EC0CB 0%, #23A3AD 100%)",
                fontWeight: 700,
                borderRadius: '12px',
                padding: '12px 32px',
                textTransform: 'none',
                fontSize: '1rem',
                boxShadow: '0 4px 20px rgba(46, 192, 203, 0.3)',
                '&:hover': {
                  background: "linear-gradient(135deg, #23A3AD 0%, #2EC0CB 100%)",
                  boxShadow: '0 6px 25px rgba(46, 192, 203, 0.4)',
                  transform: 'translateY(-1px)'
                },
                transition: 'all 0.3s ease'
              }}
            >
              Raise Support Ticket
            </Button>
          </DialogActions>
        </Dialog>
    </AuthLayout>
  );
};

export default Register;
