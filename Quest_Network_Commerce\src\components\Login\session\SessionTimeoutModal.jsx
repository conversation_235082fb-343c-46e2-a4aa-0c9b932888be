import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  Slide,
} from "@mui/material";

// Optional transition effect
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});

const SessionTimeoutModal = ({ onStay, onLogout }) => {
  return (
    <Dialog
      open
      TransitionComponent={Transition}
      keepMounted
      onClose={onStay}
      aria-describedby="session-timeout-description"
      sx={{
        "& .MuiPaper-root": {
          borderRadius: 3,
          p: 2,
          maxWidth: 400,
          mx: "auto",
        },
      }}
    >
      <DialogTitle sx={{ color: "#2EC0CB", fontWeight: "bold" }}>
        Session Expiring Soon
      </DialogTitle>
      <DialogContent>
        <DialogContentText id="session-timeout-description" sx={{ color: "text.secondary" }}>
          You’ve been inactive. Do you want to stay logged in?
        </DialogContentText>
      </DialogContent>
      <DialogActions sx={{ justifyContent: "center", gap: 2 }}>
        <Button
          onClick={onLogout}
          variant="outlined"
          color="error"
        >
          Logout
        </Button>
        <Button
          onClick={onStay}
          variant="contained"
          sx={{
            backgroundColor: "#2EC0CB",
            "&:hover": {
              backgroundColor: "#23A3AD",
            },
            color: "white",
            borderRadius: 2,
          }}
        >
          Stay Logged In
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SessionTimeoutModal;
