// src/utils/helpers.js
import { LocalShipping, Sell, Info, CreditCard, SupportAgent, Security, Inventory2, ShoppingCart, RateReview, Notifications } from '@mui/icons-material';

export function getNotificationIconAndColor(type) {
    const map = {
        order: { icon: LocalShipping, color: 'text-software-teal', border: 'border-order' },
        promotion: { icon: Sell, color: 'text-dark-blue', border: 'border-promotion' },
        system: { icon: Info, color: 'text-software-blue', border: 'border-system' },
        payment: { icon: CreditCard, color: 'text-dark-teal', border: 'border-payment' },
        support: { icon: SupportAgent, color: 'text-light-blue', border: 'border-support' },
        security: { icon: Security, color: 'text-midnight-blue', border: 'border-security' },
        stock: { icon: Inventory2, color: 'text-mid-blue', border: 'border-stock' },
        cart: { icon: ShoppingCart, color: 'text-red-700', border: 'border-red-600' },
        review: { icon: RateReview, color: 'text-green-600', border: 'border-green-600' },
        default: { icon: Notifications, color: 'text-gray-500', border: 'border-gray-300' }
    };
    return map[type] || map.default;
}

export function getPriorityBadgeClass(priority) {
    switch (priority) {
        case 'High': return 'priority-high';
        case 'Medium': return 'priority-medium';
        case 'Low': return 'priority-low';
        default: return '';
    }
}

export function formatRelativeTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const rtf = new Intl.RelativeTimeFormat('en', { numeric: 'auto' });

    const seconds = Math.round((date.getTime() - now.getTime()) / 1000);
    const minutes = Math.round(seconds / 60);
    const hours = Math.round(minutes / 60);
    const days = Math.round(hours / 24);
    const weeks = Math.round(days / 7);
    const months = Math.round(days / 30);
    const years = Math.round(days / 365);

    if (seconds > -60 && seconds < 0) return rtf.format(seconds, 'second');
    if (minutes > -60 && minutes < 0) return rtf.format(minutes, 'minute');
    if (hours > -24 && hours < 0) return rtf.format(hours, 'hour');
    if (days > -7 && days < 0) return rtf.format(days, 'day');
    if (weeks > -4 && weeks < 0) return rtf.format(weeks, 'week');
    if (months > -12 && months < 0) return rtf.format(months, 'month');
    if (years < 0) return rtf.format(years, 'year');

    return 'Just now';
}

export const sortNotifications = (notifications) => {
    return [...notifications].sort((a, b) => {
        const priorityOrder = { 'High': 1, 'Medium': 2, 'Low': 3 };
        const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return new Date(b.timestamp) - new Date(a.timestamp);
    });
};